<?php
    $title ??= $shortcode->title;
    $subtitle ??= $shortcode->subtitle;
?>

<?php if($title || $subtitle): ?>
    <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['tp-section-title-wrapper', $class ?? null]); ?>">
        <?php if($subtitle): ?>
            <span class="tp-section-title-pre">
                <?php echo BaseHelper::clean($subtitle); ?>

            </span>
        <?php endif; ?>
        <?php if($title): ?>
            <h3 class="section-title tp-section-title">
                <?php echo $__env->make(Theme::getThemeNamespace('partials.section-title-inner'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </h3>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/partials/section-title.blade.php ENDPATH**/ ?>