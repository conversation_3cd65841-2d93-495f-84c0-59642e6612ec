{"(:count reviews)": "(:count reviews)", "(:count)": "(:count)", "(Shipping fees not included)": "(Shipping fees not included)", "- (dash)": "- (dash)", "-- None --": "-- None --", "-- Select --": "-- Select --", "1 Review": "1 Review", "1 product": "1 product", "404 Page Not Found": "404 Page Not Found", "404 page image": "404 page image", "500 Internal Server Error": "500 Internal Server Error", "503 Service Unavailable": "503 Service Unavailable", ":count Reviews": ":count Reviews", ":count decrease": ":count decrease", ":count increase": ":count increase", ":count more": ":count more", ":count products": ":count products", ":customer has requested return product(s)": ":customer has requested return product(s)", ":days D": ":days D", ":hours H": ":hours H", ":minutes M": ":minutes M", ":name doesn't support :currency. List of currencies supported by :name: :currencies.": ":name doesn't support :currency. List of currencies supported by :name: :currencies.", ":name font family": ":name font family", ":name font size": ":name font size", ":number Star": ":number Star", ":number Stars": ":number Stars", ":number product available": ":number product available", ":number products available": ":number products available", ":product is already in your compare list!": ":product is already in your compare list!", ":seconds S": ":seconds S", ":total Product found": ":total Product found", ":total Products found": ":total Products found", ":total review(s) \":star star\" for \":product\"": ":total review(s) \":star star\" for \":product\"", ":total review(s) for \":product\"": ":total review(s) for \":product\"", "A new version (:version / released on :date) is available to update!": "A new version (:version / released on :date) is available to update!", "API Key": "API Key", "About": "About", "About the store": "About the store", "Accept and install": "Accept and install", "Account": "Account", "Account Dashboard": "Account Dashboard", "Account Holder Name": "Account Holder Name", "Account Menu": "Account <PERSON><PERSON>", "Account Number": "Account Number", "Account Settings": "Account <PERSON><PERSON>", "Account information": "Account information", "Action": "Action", "Action Label": "Action Label", "Action URL": "Action URL", "Action label": "Action label", "Actions": "Actions", "Active": "Active", "Ad :number": "Ad :number", "Add Another Address": "Add Another Address", "Add Google Maps iframe": "Add Google Maps iframe", "Add New Product": "Add New Product", "Add Product": "Add Product", "Add To Cart": "Add To Cart", "Add To Compare": "Add To Compare", "Add To Wishlist": "Add To Wishlist", "Add Wishlist": "Add Wishlist", "Add YouTube video": "Add YouTube video", "Add Your First Address": "Add Your First Address", "Add a custom menu to your widget area.": "Add a custom menu to your widget area.", "Add a new address": "Add a new address", "Add custom HTML content": "Add custom HTML content", "Add multiple addresses for different shipping locations or billing purposes.": "Add multiple addresses for different shipping locations or billing purposes.", "Add new": "Add new", "Add new address...": "Add new address...", "Add to Cart": "Add to Cart", "Add to cart": "Add to cart", "Add your first shipping or billing address to get started.": "Add your first shipping or billing address to get started.", "Add your review": "Add your review", "Added product :product successfully!": "Added product :product successfully!", "Added product :product to cart successfully!": "Added product :product to cart successfully!", "Added product :product to compare list successfully!": "Added product :product to compare list successfully!", "Added review successfully!": "Added review successfully!", "Address": "Address", "Address appears to be incomplete": "Address appears to be incomplete", "Address books": "Address books", "Address:": "Address:", "Addresses": "Addresses", "Admin": "Admin", "Ads": "Ads", "After cancel amount and fee will be refunded back in your balance": "After cancel amount and fee will be refunded back in your balance", "After creating the webhook, Razorpay will generate a <strong>Webhook Secret</strong>. Copy this secret and paste it into the <strong>Webhook Secret</strong> field in the settings form. This is required for secure webhook verification.": "After creating the webhook, <PERSON><PERSON><PERSON><PERSON> will generate a <strong>Webhook Secret</strong>. Copy this secret and paste it into the <strong>Webhook Secret</strong> field in the settings form. This is required for secure webhook verification.", "After registration at :name, you will have API key": "After registration at :name, you will have API key", "After registration at :name, you will have Client ID, Client Secret": "After registration at :name, you will have Client ID, Client Secret", "After registration at :name, you will have Public & Secret keys": "After registration at :name, you will have Public & Secret keys", "After registration at :name, you will have Store ID and Store Password (API/Secret key)": "After registration at :name, you will have Store ID and Store Password (API/Secret key)", "After the first image": "After the first image", "Agree terms and policy": "Agree terms and policy", "Align center": "Align center", "Align start": "Align start", "All": "All", "All Categories": "All Categories", "All Pages": "All Pages", "All Products": "All Products", "All categories": "All categories", "All caught up!": "All caught up!", "All files": "All files", "All messages are recorded and spam is not tolerated. Your email address will be shown to the recipient.": "All messages are recorded and spam is not tolerated. Your email address will be shown to the recipient.", "Already have an account?": "Already have an account?", "Amount": "Amount", "An error occurred while trying to login": "An error occurred while trying to login", "App Downloads": "App Downloads", "Apple URL": "Apple URL", "Apple icon": "Apple icon", "Apple label": "Apple label", "Apple token invalid": "Apple token invalid", "Applied coupon \":code\" successfully!": "Applied coupon \":code\" successfully!", "Apply": "Apply", "Are you sure you have received your order? Please confirm that the package has been delivered to you?": "Are you sure you have received your order? Please confirm that the package has been delivered to you?", "Are you sure you want to delete this address?": "Are you sure you want to delete this address?", "Are you sure you want to delete your review?": "Are you sure you want to delete your review?", "Are you sure you want to turn off the debug mode? This action cannot be undone.": "Are you sure you want to turn off the debug mode? This action cannot be undone.", "Are you sure?": "Are you sure?", "At <strong>Active Events</strong> field, make sure to enable the following events:": "At <strong>Active Events</strong> field, make sure to enable the following events:", "Attract your customers with the best products.": "Attract your customers with the best products.", "Audio File": "Audio File", "Author Avatar": "Author <PERSON><PERSON>", "Author Description": "Author Description", "Author Name": "Author Name", "Author Role": "Author Role", "Author Signature": "Author Signature", "Author URL": "Author URL", "Auto open mini cart when product is added to cart?": "Auto open mini cart when product is added to cart?", "Autoplay speed (if autoplay enabled)": "Autoplay speed (if autoplay enabled)", "Available": "Available", "Avg. Order Value": "Avg. Order Value", "Back": "Back", "Back to Blog": "Back to Blog", "Back to Home": "Back to Home", "Back to cart": "Back to cart", "Back to login page": "Back to login page", "Back to shopping": "Back to shopping", "Background color": "Background color", "Background color (optional)": "Background color (optional)", "Background color is light?": "Background color is light?", "Background image": "Background image", "Background image (optional)": "Background image (optional)", "Balance": "Balance", "Bank Code/IFSC": "Bank Code/IFSC", "Bank Name": "Bank Name", "Bank transfer amount: <strong>:amount</strong>": "Bank transfer amount: <strong>:amount</strong>", "Bank transfer description: <strong>Payment for order :code</strong>": "Bank transfer description: <strong>Payment for order :code</strong>", "Banner": "Banner", "Barcode \":value\" has been duplicated on row #:row": "Barcode \":value\" has been duplicated on row #:row", "Become A Vendor": "Become A Vendor", "Become Vendor": "<PERSON>come Vendor", "Before the last image": "Before the last image", "Below :toPrice": "Below :toP<PERSON>", "Billing information": "Billing information", "Blog About Me": "Blog About Me", "Blog Categories": "Blog Categories", "Blog Posts": "Blog Posts", "Blog Search": "Blog Search", "Blog sidebar": "Blog sidebar", "Body": "Body", "Body attributes": "Body attributes", "Border color": "Border color", "Bottom": "Bottom", "Bottom Bar Menu": "Bottom Bar Menu", "Bought Together": "Bought Together", "Brands": "Brands", "Breadcrumb background": "Breadcrumb background", "Breadcrumb background color": "Breadcrumb background color", "Breadcrumb background image": "Breadcrumb background image", "Breadcrumb height (px)": "Breadcrumb height (px)", "Breadcrumb reduce length on mobile": "Breadcrumb reduce length on mobile", "Breadcrumb style": "Breadcrumb style", "Browse Products": "Browse Products", "Business Name": "Business Name", "Business hours": "Business hours", "Button Label": "Button Label", "Button Label (for Style 1)": "Button Label (for Style 1)", "Button URL": "Button URL", "Button URL (for Style 1)": "Button URL (for Style 1)", "Button label": "Button label", "Button view more URL": "Button view more URL", "Button view more label": "Button view more label", "Buy Now": "Buy Now", "Buy now at :price": "Buy now at :price", "By :name": "By :name", "COD (Cash On Delivery) payment method is not available for digital products only.": "COD (Cash On Delivery) payment method is not available for digital products only.", "Can't send message on this time, please try again later!": "Can't send message on this time, please try again later!", "Cancel": "Cancel", "Cancel Order": "Cancel Order", "Cancel order": "Cancel order", "Cancel return order with reason: :reason": "Cancel return order with reason: :reason", "Cancellation Reason": "Cancellation Reason", "Cannot download files": "Cannot download files", "Cannot find this customer!": "Cannot find this customer!", "Cannot found files": "Cannot found files", "Cannot login, no email or Apple ID provided!": "Cannot login, no email or Apple ID provided!", "Cannot login, no email or Facebook ID provided!": "Cannot login, no email or Facebook ID provided!", "Cannot login, no email or Google ID provided!": "Cannot login, no email or Google ID provided!", "Cannot login, no email provided!": "Cannot login, no email provided!", "Captcha": "<PERSON><PERSON>", "Captcha Verification Failed!": "Captcha Verification Failed!", "Cart": "<PERSON><PERSON>", "Cart is empty": "Cart is empty", "Cart item is not existed!": "Cart item is not existed!", "Cart item not found": "Cart item not found", "Cart item removed successfully": "Cart item removed successfully", "Categories": "Categories", "Category:": "Category:", "Center Video": "Center Video", "Centered text": "Centered text", "Certificate of Incorporation": "Certificate of Incorporation", "Change Password": "Change Password", "Change avatar": "Change avatar", "Change copyright": "Change copyright", "Change password": "Change password", "Check": "Check", "Checkout": "Checkout", "Checkout error!": "Checkout error!", "Checkout is only available for products from one store at a time. Please remove items from other stores before proceeding.": "Checkout is only available for products from one store at a time. Please remove items from other stores before proceeding.", "Checkout successfully!": "Checkout successfully!", "Choose Reason": "Choose <PERSON>", "Choose a Reason for Order Cancellation": "Choose a Reason for Order Cancellation", "Choose a reason...": "Choose a reason...", "Choose brands": "Choose brands", "Choose brands to display": "Choose brands to display", "Choose categories": "Choose categories", "Choose collections": "Choose collections", "Choose date format for your front theme.": "Choose date format for your front theme.", "Choose products": "Choose products", "City": "City", "Clear all": "Clear all", "Close": "Close", "Collections": "Collections", "Color": "Color", "Columns": "Columns", "Coming Soon": "Coming Soon", "Company address": "Company address", "Company email": "Company email", "Company name": "Company name", "Company tax code": "Company tax code", "Compare": "Compare", "Completed At": "Completed At", "Completed at": "Completed at", "Confirm": "Confirm", "Confirm Delivery": "Confirm Delivery", "Confirm password": "Confirm password", "Confirm your password": "Confirm your password", "Congratulations on being a vendor at :site_title": "Congratulations on being a vendor at :site_title", "Contact Us": "Contact Us", "Content": "Content", "Continue Shopping": "Continue Shopping", "Continue shopping": "Continue shopping", "Continuously": "Continuously", "Conversion Rate": "Conversion Rate", "Copied!": "Copied!", "Copy link": "Copy link", "Copyright": "Copyright", "Copyright on footer of site. Using %Y to display current year.": "Copyright on footer of site. Using %Y to display current year.", "Copyright text at the bottom footer.": "Copyright text at the bottom footer.", "Could not download updated file. Please check your license or your internet network.": "Could not download updated file. Please check your license or your internet network.", "Could not update files & database.": "Could not update files & database.", "Countdown time": "Countdown time", "Country": "Country", "Coupon": "Coupon", "Coupon Code:": "Coupon Code:", "Coupon code": "Coupon code", "Coupon code discount amount": "Coupon code discount amount", "Coupon code is not valid or does not apply to the products": "Coupon code is not valid or does not apply to the products", "Coupon code: \":code\"": "Coupon code: \":code\"", "Coupon code: :code": "Coupon code: :code", "Coupon codes (:count)": "Coupon codes (:count)", "Coupons": "Coupons", "Cover Image": "Cover Image", "Create": "Create", "Create Address": "Create Address", "Create Discount": "Create Discount", "Create a new product <a href=\":url\">here</a>": "Create a new product <a href=\":url\">here</a>", "Create coupon": "Create coupon", "Created At": "Created At", "Created at": "Created at", "Created shipment for order": "Created shipment for order", "Currency: :currency": "Currency: :currency", "Current password": "Current password", "Custom": "Custom", "Custom CSS (optional)": "Custom CSS (optional)", "Custom Fields": "Custom Fields", "Custom HTML": "Custom HTML", "Custom Menu": "Custom Menu", "Customer": "Customer", "Customer ID must be a number!": "Customer ID must be a number!", "Customer Recently Viewed Products": "Customer Recently Viewed Products", "Customer Reviews": "Customer Reviews", "Customer can buy product and pay directly using Visa, Credit card via :name": "Customer can buy product and pay directly using Visa, Credit card via :name", "Customer forgot password form": "Customer forgot password form", "Customer information": "Customer information", "Customer login form": "Customer login form", "Customer register form": "Customer register form", "Customer reset password form": "Customer reset password form", "Customer reviews": "Customer reviews", "Customize font family of description text?": "Customize font family of description text?", "Customize the blog page with this sidebar widget.": "Customize the blog page with this sidebar widget.", "Customize the footer content with this sidebar widget.": "Customize the footer content with this sidebar widget.", "Customize the product details page with this sidebar widget.": "Customize the product details page with this sidebar widget.", "Customize the products by brand page with this sidebar widget.": "Customize the products by brand page with this sidebar widget.", "Customize the products by category page with this sidebar widget.": "Customize the products by category page with this sidebar widget.", "Customize the products by tag page with this sidebar widget.": "Customize the products by tag page with this sidebar widget.", "Customize the products listing page with this sidebar widget.": "Customize the products listing page with this sidebar widget.", "Dashboard": "Dashboard", "Date": "Date", "Date Shipped": "Date Shipped", "Date format": "Date format", "Date of birth": "Date of birth", "Days": "Days", "Default": "<PERSON><PERSON><PERSON>", "Default (from testimonial)": "De<PERSON>ult (from testimonial)", "Default blog posts layout": "Default blog posts layout", "Delete": "Delete", "Delete Account": "Delete Account", "Delete your account": "Delete your account", "Delete your review": "Delete your review", "Deleted review successfully!": "Deleted review successfully!", "Delivered": "Delivered", "Delivery Notes": "Delivery Notes", "Delivery Notes:": "Delivery Notes:", "Delivery confirmed successfully": "Delivery confirmed successfully", "Description": "Description", "Detail Page (after)": "Detail <PERSON> (after)", "Detail Page (before)": "Detail <PERSON> (before)", "Device ID is required!": "Device ID is required!", "Digital Product": "Digital Product", "Discount": "Discount", "Discount :amount": "Discount :amount", "Discount :percentage%": "Discount :percentage%", "Discount amount": "Discount amount", "Dismiss": "<PERSON><PERSON><PERSON>", "Display Newsletter form on sidebar": "Display Newsletter form on sidebar", "Display about me widget": "Display about me widget", "Display accepted payments image or whatever image you want.": "Display accepted payments image or whatever image you want.", "Display blog posts": "Display blog posts", "Display brands list": "Display brands list", "Display children categories": "Display children categories", "Display copyright text and partner images in the lower section of your website's footer.": "Display copyright text and partner images in the lower section of your website's footer.", "Display extra information for product detail page.": "Display extra information for product detail page.", "Display on pages": "Display on pages", "Display posts count?": "Display posts count?", "Display recent blog posts": "Display recent blog posts", "Display site contact information.": "Display site contact information.", "Display type": "Display type", "Do you really want to delete the review?": "Do you really want to delete the review?", "Do you want to cancel?": "Do you want to cancel?", "Do you want to delete this image?": "Do you want to delete this image?", "Documents": "Documents", "Don't have an account?": "Don't have an account?", "Don't show this popup again": "Don't show this popup again", "Download all files": "Download all files", "Download invoice": "Download invoice", "Download product \":name\" with external links": "Download product \":name\" with external links", "Downloaded": "Downloaded", "Downloads": "Downloads", "Drop Certificate of Incorporation here or click to upload": "Drop Certificate of Incorporation here or click to upload", "Drop Government ID here or click to upload": "Drop Government ID here or click to upload", "Drop files here or click to upload.": "Drop files here or click to upload.", "Dynamic carousel for featured content with customizable links.": "Dynamic carousel for featured content with customizable links.", "Earnings": "Earnings", "Earnings in :label": "Earnings in :label", "Ecommerce Brands": "Ecommerce Brands", "Ecommerce Categories": "Ecommerce Categories", "Ecommerce Collections": "Ecommerce Collections", "Ecommerce Coupons": "Ecommerce Coupons", "Ecommerce Flash Sale": "Ecommerce Flash Sale", "Ecommerce Product Groups": "Ecommerce Product Groups", "Ecommerce Products": "Ecommerce Products", "Edit": "Edit", "Edit Account": "Edit Account", "Edit Address #:id": "Edit Address #:id", "Edit this shortcode": "Edit this shortcode", "Edit this widget": "Edit this widget", "Edit your profile and account details": "Edit your profile and account details", "Either email or phone is required": "Either email or phone is required", "Email": "Email", "Email (optional)": "Email (optional)", "Email :store": "Email :store", "Email Address": "Email Address", "Email address": "Email address", "Email is required": "Email is required", "Email or Phone number": "Email or Phone number", "Email or phone": "Email or phone", "Email:": "Email:", "Empty Cart": "Empty Cart", "Empty cart image": "Empty cart image", "Empty cart successfully!": "Empty cart successfully!", "Enable Facebook comment in post detail page?": "Enable Facebook comment in post detail page?", "Enable Facebook comment in the product page?": "Enable Facebook comment in the product page?", "Enable Newsletter Popup": "Enable Newsletter Popup", "Enable Preloader?": "Enable Preloader?", "Enable animation?": "Enable animation?", "Enable back to top button": "Enable back to top button", "Enable bottom menu bar on mobile": "Enable bottom menu bar on mobile", "Enable dark mode": "Enable dark mode", "Enable header categories dropdown on mobile?": "Enable header categories dropdown on mobile?", "Enable header categories dropdown?": "Enable header categories dropdown?", "Enable lazy loading": "Enable lazy loading", "Enable light mode": "Enable light mode", "Enable mega menu in product categories dropdown?": "Enable mega menu in product categories dropdown?", "Enable section title shape decorated": "Enable section title shape decorated", "Enable sticky header": "Enable sticky header", "Enable sticky header on mobile": "Enable sticky header on mobile", "End date": "End date", "Engage visitors before they reach the footer with this widget.": "Engage visitors before they reach the footer with this widget.", "Ensure your account is using a long, random password to stay secure.": "Ensure your account is using a long, random password to stay secure.", "Enter API key into the box in right hand": "Enter API key into the box in right hand", "Enter Apple URL": "Enter Apple URL", "Enter Apple label": "Enter Apple label", "Enter Client ID, Secret into the box in right hand": "Enter Client ID, Secret into the box in right hand", "Enter Coupon Code": "Enter Coupon Code", "Enter Google URL": "Enter Google URL", "Enter Google label": "Enter Google label", "Enter Public, Secret into the box in right hand": "Enter Public, Secret into the box in right hand", "Enter Store ID and Store Password (API/Secret key) into the box in right hand": "Enter Store ID and Store Password (API/Secret key) into the box in right hand", "Enter Your Email": "Enter Your Email", "Enter coupon code...": "Enter coupon code...", "Enter the order ID": "Enter the order ID", "Enter the size of the icon image in pixels. It is used when the icon image is set.": "Enter the size of the icon image in pixels. It is used when the icon image is set.", "Enter your business email address where invoices and tax documents will be sent (e.g., <EMAIL>).": "Enter your business email address where invoices and tax documents will be sent (e.g., <EMAIL>).", "Enter your business tax identification number such as Tax ID, VAT number, or EIN (e.g., 12-3456789, VAT123456789, EIN 12-3456789).": "Enter your business tax identification number such as Tax ID, VAT number, or EIN (e.g., 12-3456789, VAT123456789, EIN 12-3456789).", "Enter your complete business address including street, city, state, and postal code (e.g., 123 Business Street, Suite 100, City, State 12345).": "Enter your complete business address including street, city, state, and postal code (e.g., 123 Business Street, Suite 100, City, State 12345).", "Enter your current password": "Enter your current password", "Enter your email": "Enter your email", "Enter your phone number": "Enter your phone number", "Enter your registered business or company name as it appears on official documents (e.g., ABC Corporation Ltd.).": "Enter your registered business or company name as it appears on official documents (e.g., ABC Corporation Ltd.).", "Error": "Error", "Error when processing payment via :paymentType!": "Error when processing payment via :paymentType!", "Estimate Date Shipped": "Estimate Date Shipped", "Ex: 0943243332": "Ex: 0943243332", "Ex: My Shop": "Ex: My Shop", "Exception": "Exception", "Excluding :tax": "Excluding :tax", "Expand the content of the first FAQ": "Expand the content of the first FAQ", "Expired": "Expired", "Explore and add items to get started": "Explore and add items to get started", "External link downloads": "External link downloads", "FAQ categories": "FAQ categories", "FAQs": "FAQs", "Facebook": "Facebook", "Facebook Admin ID": "Facebook Admin ID", "Facebook Admins": "Facebook Admins", "Facebook App ID": "Facebook App ID", "Facebook Integration": "Facebook Integration", "Facebook admins to manage comments :link": "Facebook admins to manage comments :link", "Facebook authentication is not properly configured": "Facebook authentication is not properly configured", "Facebook page ID": "Facebook page ID", "Facebook token invalid": "Facebook token invalid", "Failed to return the order": "Failed to return the order", "Featured": "Featured", "Fee": "Fee", "Fees": "Fees", "Fill yellow color to star icons": "Fill yellow color to star icons", "Filter": "Filter", "Fix it for me": "Fix it for me", "Flash Sale end in:": "Flash Sale end in:", "Font family for description text": "Font family for description text", "Footer (after)": "Footer (after)", "Footer (before)": "Footer (before)", "Footer Bottom sidebar": "Footer Bottom sidebar", "Footer Primary sidebar": "Footer Primary sidebar", "Footer Top sidebar": "Footer Top sidebar", "Footer background color": "Footer background color", "Footer border color": "Footer border color", "Footer link color": "Footer link color", "Footer link hover color": "Footer link hover color", "Footer text color": "Footer text color", "Footer title color": "Footer title color", "For devices with width from 768px to 1200px, if empty, will use the image from the desktop.": "For devices with width from 768px to 1200px, if empty, will use the image from the desktop.", "For devices with width less than 768px, if empty, will use the image from the tablet.": "For devices with width less than 768px, if empty, will use the image from the tablet.", "Forgot Password": "Forgot Password", "Forgot password?": "Forgot password?", "Free": "Free", "Free Shipping": "Free Shipping", "Free shipping": "Free shipping", "Free shipping for all orders": "Free shipping for all orders", "Free shipping to <strong>:target</strong>": "Free shipping to <strong>:target</strong>", "From :fromPrice to :toPrice": "From :fromPrice to :toPrice", "Fulfillment Rate": "Fulfillment Rate", "Full Name": "Full Name", "Full name": "Full name", "Full width": "Full width", "Full width style will only display the slider image without any text or button. The recommended image dimension is 1920x512 px.": "Full width style will only display the slider image without any text or button. The recommended image dimension is 1920x512 px.", "Functions": "Functions", "Funding Source": "Funding Source", "Galleries": "Galleries", "Get data from to show": "Get data from to show", "Get it on": "Get it on", "Get webhook secret key from your Razorpay dashboard to verify webhook requests": "Get webhook secret key from your Razorpay dashboard to verify webhook requests", "Gift": "Gift", "Go To Cart": "Go To Cart", "Go back home": "Go back home", "Go to :link to change the copyright text.": "Go to :link to change the copyright text.", "Go to Shop": "Go to Shop", "Go to homepage": "Go to homepage", "Google Maps": "Google Maps", "Google Play icon": "Google Play icon", "Google URL": "Google URL", "Google authentication is not properly configured": "Google authentication is not properly configured", "Google label": "Google label", "Google token invalid": "Google token invalid", "Government ID": "Government ID", "Grid": "Grid", "Group by category": "Group by category", "Groups": "Groups", "Guest": "Guest", "HTML attributes": "HTML attributes", "HTML code": "HTML code", "Has sidebar": "Has sidebar", "Header (after)": "Header (after)", "Header (before)": "Header (before)", "Header :number": "Header :number", "Header border color": "Header border color", "Header main background color": "Header main background color", "Header main text color": "Header main text color", "Header menu background color": "Header menu background color", "Header menu text color": "Header menu text color", "Header style": "Header style", "Header top background color": "Header top background color", "Header top text color": "Header top text color", "Heading 1": "Heading 1", "Heading 2": "Heading 2", "Heading 3": "Heading 3", "Heading 4": "Heading 4", "Heading 5": "Heading 5", "Heading 6": "Heading 6", "Height": "Height", "Hello": "Hello", "Hello, :name": "Hello, :name", "Hide rating star when is zero?": "Hide rating star when is zero?", "Hide title?": "Hide title?", "Home": "Home", "Homepage": "Homepage", "Horizontal": "Horizontal", "Hotline": "Hotline", "Hotline:": "Hotline:", "Hrs": "Hrs", "Humanitarian Donation": "Humanitarian Donation", "I agree to the :link": "I agree to the :link", "I agree to the Terms and Privacy Policy": "I agree to the Terms and Privacy Policy", "I am a customer": "I am a customer", "I am a vendor": "I am a vendor", "ID number": "ID number", "Icon": "Icon", "Icon Image (It will override icon above if set)": "Icon Image (It will override icon above if set)", "Icon color": "Icon color", "Icon image": "Icon image", "Icon image (It will override icon above if set)": "Icon image (It will override icon above if set)", "Icon image size (px)": "Icon image size (px)", "If enabled, you can select font family for description text. Otherwise, it will use the default font family.": "If enabled, you can select font family for description text. Otherwise, it will use the default font family.", "If yes, only featured products will be shown, it is helpful if you want to add a section for featured products.": "If yes, only featured products will be shown, it is helpful if you want to add a section for featured products.", "If you are the administrator and you can't access your site after enabling maintenance mode, just need to delete file <strong>storage/framework/down</strong> to turn-off maintenance mode.": "If you are the administrator and you can't access your site after enabling maintenance mode, just need to delete file <strong>storage/framework/down</strong> to turn-off maintenance mode.", "If you need help, contact us at :mail.": "If you need help, contact us at :mail.", "If you need multiple emails, please use slash (/) to separate them. e.g: <EMAIL>/<EMAIL>": "If you need multiple emails, please use slash (/) to separate them. e.g: <EMAIL>/<EMAIL>", "If you need multiple phones, please use slash (/) to separate them. e.g: 012345566/0345678923": "If you need multiple phones, please use slash (/) to separate them. e.g: 012345566/0345678923", "If you select an image, the background color will be ignored.": "If you select an image, the background color will be ignored.", "Image": "Image", "Image 1": "Image 1", "Image 2": "Image 2", "Image Slider": "Image Slider", "Image size should be 1920x200px": "Image size should be 1920x200px", "Images from customer (:count)": "Images from customer (:count)", "In Stock": "In Stock", "In Transit": "In Transit", "In stock": "In stock", "Includes Completed, Pending, and Processing statuses": "Includes Completed, Pending, and Processing statuses", "Including :tax": "Including :tax", "Install plugin from Marketplace": "Install plugin from Marketplace", "Instructions": "Instructions", "Insufficient balance or no bank information": "Insufficient balance or no bank information", "Internal Server Error": "Internal Server Error", "Invalid Apple token": "Invalid Apple token", "Invalid Data!": "Invalid Data!", "Invalid Facebook token": "Invalid Facebook token", "Invalid Google token": "Invalid Google token", "Invalid Transaction!": "Invalid Transaction!", "Invalid data send": "Invalid data send", "Invalid guard configuration": "Invalid guard configuration", "Invalid signature of vendor info": "Invalid signature of vendor info", "Invalid step.": "Invalid step.", "InvalidStateException occurred while trying to login": "InvalidStateException occurred while trying to login", "Inventory Status": "Inventory Status", "Invoice detail :code": "Invoice detail :code", "Invoices": "Invoices", "Is autoplay?": "Is autoplay?", "It is important to enable <strong>ALL</strong> these events to ensure your system captures all payment statuses correctly. Missing events may result in payments not being recorded in your system.": "It is important to enable <strong>ALL</strong> these events to ensure your system captures all payment statuses correctly. Missing events may result in payments not being recorded in your system.", "It is optional. If you have UPI ID, you can provide it here. Learn more: https://support.google.com/pay/india/answer/10331134?hl=en": "It is optional. If you have UPI ID, you can provide it here. Learn more: https://support.google.com/pay/india/answer/10331134?hl=en", "It looks as through there are no activities here.": "It looks as through there are no activities here.", "It looks as through there are no request errors here.": "It looks as through there are no request errors here.", "It will replace Icon Font if it is present.": "It will replace Icon Font if it is present.", "Items": "Items", "Items Count": "Items Count", "Items Earning Sales: :amount": "Items Earning Sales: :amount", "Items per row (if style is grid)": "Items per row (if style is grid)", "Items per view": "Items per view", "Joined :date": "Joined :date", "Joined on :date": "Joined on :date", "Just used when preloader is enabled and version is Theme build-in": "Just used when preloader is enabled and version is Theme build-in", "Key": "Key", "Language": "Language", "Last update": "Last update", "Latest": "Latest", "Lazy load images": "Lazy load images", "Lazy load placeholder image (250x250px)": "Lazy load placeholder image (250x250px)", "Learn more": "Learn more", "Learn more about Twig template: :url": "Learn more about Twig template: :url", "Leave categories empty if you want to show posts from all categories.": "Leave categories empty if you want to show posts from all categories.", "Leave empty to hide button": "Leave empty to hide button", "Leave empty to link to the blog page": "Leave empty to link to the blog page", "Leave empty to link to the category page": "Leave empty to link to the category page", "Leave empty to link to the shop page": "Leave empty to link to the shop page", "Leave empty to show all brands": "Leave empty to show all brands", "Leave empty to use default height.": "Leave empty to use default height.", "Leave empty to use the category image": "Leave empty to use the category image", "Leave empty to use the default logo in Theme Options.": "Leave empty to use the default logo in Theme Options.", "Leave it empty to use the default description from Theme options -> General.": "Leave it empty to use the default description from Theme options -> General.", "Left :left": "Left :left", "Left margin in LTR, right margin in RTL": "Left margin in LTR, right margin in RTL", "Left sidebar": "Left sidebar", "License": "License", "License Activation": "License Activation", "Limit": "Limit", "LinkedIn": "LinkedIn", "List": "List", "List of product categories": "List of product categories", "Listing Page (after)": "Listing Page (after)", "Listing Page (before)": "Listing Page (before)", "Loading": "Loading", "Log files": "Log files", "Login": "<PERSON><PERSON>", "Login / Register": "Login / Register", "Login successful": "Login successful", "Login to your account": "Login to your account", "Login with social networks": "Login with social networks", "Logo": "Logo", "Logo height (default: 35px)": "Logo height (default: 35px)", "Logo height (px)": "Logo height (px)", "Logo light": "Logo light", "Logout": "Logout", "Looks like there are no reviews yet.": "Looks like there are no reviews yet.", "Looks like we don't have any posts matching your search.": "Looks like we don't have any posts matching your search.", "Loop?": "Loop?", "Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.": "Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.", "Low Stock": "Low Stock", "Manage Addresses": "Manage Addresses", "Manage Invoices": "Manage Invoices", "Manage the social links in Theme Options -> Social Links": "Manage the social links in Theme Options -> Social Links", "Manage your account, view orders, and update your preferences from your personal dashboard.": "Manage your account, view orders, and update your preferences from your personal dashboard.", "Manage your shipping and billing addresses": "Manage your shipping and billing addresses", "Margin Bottom (px)": "<PERSON>gin Bottom (px)", "Margin End (px)": "<PERSON>gin End (px)", "Margin Start (px)": "<PERSON>gin Start (px)", "Margin Top (px)": "<PERSON>gin Top (px)", "Marketplace Stores": "Marketplace Stores", "Math Captcha": "<PERSON>", "Math Captcha Verification Failed!": "Math Captcha Verification Failed!", "Maximum order quantity is :qty, please check your cart and retry again!": "Maximum order quantity is :qty, please check your cart and retry again!", "Maximum order quantity of product :product is :quantity!": "Maximum order quantity of product :product is :quantity! ", "Maximum quantity is :max!": "Maximum quantity is :max!", "Media - Audio": "Media - Audio", "Media - Video": "Media - Video", "Media URL": "Media URL", "Menu": "<PERSON><PERSON>", "Menu text font size": "Menu text font size", "Merchandise": "Merchandise", "Message": "Message", "Messages": "Messages", "Minimal": "Minimal", "Minimum order amount is :amount, you need to buy more :more to place an order!": "Minimum order amount is :amount, you need to buy more :more to place an order!", "Minimum order amount to use :name payment method is :amount, you need to buy more :more to place an order!": "Minimum order amount to use :name payment method is :amount, you need to buy more :more to place an order!", "Minimum order amount to use Bank Transfer payment method is :amount, you need to buy more :more to place an order!": "Minimum order amount to use Bank Transfer payment method is :amount, you need to buy more :more to place an order!", "Minimum order amount to use COD (Cash On Delivery) payment method is :amount, you need to buy more :more to place an order!": "Minimum order amount to use COD (Cash On Delivery) payment method is :amount, you need to buy more :more to place an order!", "Minimum order quantity is :qty, you need to buy more :more to place an order!": "Minimum order quantity is :qty, you need to buy more :more to place an order!", "Minimum order quantity of product :product is :quantity, you need to buy more :more to place an order!": "Minimum order quantity of product :product is :quantity, you need to buy more :more to place an order! ", "Mins": "<PERSON>s", "Minus": "Minus", "Missing documentations! Please upload your certificate of incorporation and government ID to continue.": "Missing documentations! Please upload your certificate of incorporation and government ID to continue.", "Mobile Image": "Mobile Image", "Mobile screenshot": "Mobile screenshot", "Moderator's note": "Moderator's note", "More...": "More...", "My Profile": "My Profile", "Name": "Name", "Name: A-Z": "Name: A-<PERSON>", "Name: Z-A": "Name: Z-<PERSON>", "Need another address?": "Need another address?", "New password": "New password", "Newest": "Newest", "Newsletter": "Newsletter", "Newsletter Popup": "Newsletter Popup", "Newsletter form": "Newsletter form", "No": "No", "No addresses yet!": "No addresses yet!", "No coupon code found": "No coupon code found", "No digital products!": "No digital products!", "No files found": "No files found", "No galleries found": "No galleries found", "No order return requests yet!": "No order return requests yet!", "No orders yet!": "No orders yet!", "No orders!": "No orders!", "No payment charge. Please try again!": "No payment charge. Please try again!", "No products in cart": "No products in cart", "No products in cart. :link!": "No products in cart. :link!", "No products pending review": "No products pending review", "No products were found matching your selection.": "No products were found matching your selection.", "No products!": "No products!", "No results found": "No results found", "No results found!": "No results found!", "No reviews yet": "No reviews yet", "No reviews yet!": "No reviews yet!", "No reviews!": "No reviews!", "No shipping methods available!": "No shipping methods available!", "No shipping methods were found with your provided shipping information!": "No shipping methods were found with your provided shipping information!", "No sidebar": "No sidebar", "None": "None", "Not Available": "Not Available", "Not available": "Not available", "Not available in COD payment option.": "Not available in COD payment option.", "Not specified": "Not specified", "Note": "Note", "Notes about your order, e.g. special notes for delivery.": "Notes about your order, e.g. special notes for delivery.", "Number of deal products to show": "Number of deal products to show", "Number of galleries to show. Set to 0 or leave it empty to show all. It will be overridden if you select galleries below.": "Number of galleries to show. Set to 0 or leave it empty to show all. It will be overridden if you select galleries below.", "Number of posts to show": "Number of posts to show", "Number of products per row": "Number of products per row", "Number of products per row on mobile": "Number of products per row on mobile", "Number of products to show": "Number of products to show", "Number tags to display": "Number tags to display", "Oldest": "Oldest", "On Sale": "On Sale", "On sale": "On sale", "One or all products are not enough quantity so cannot update!": "One or all products are not enough quantity so cannot update!", "Oops! Page not found": "Oops! Page not found", "Oops! Something Went Wrong.": "Oops! Something Went Wrong.", "Open user menu": "Open user menu", "Or you can upload a new one, the old one will be replaced.": "Or you can upload a new one, the old one will be replaced.", "Order": "Order", "Order Details": "Order Details", "Order ID": "Order ID", "Order ID number": "Order ID number", "Order Information": "Order Information", "Order Return Request not found!": "Order Return Request not found!", "Order Return Requests": "Order Return Requests", "Order Return Requests :id": "Order Return Requests :id", "Order Returns": "Order Returns", "Order Tracking": "Order Tracking", "Order cancelled successfully": "Order cancelled successfully", "Order code is required": "Order code is required", "Order completed": "Order completed", "Order detail :id": "Order detail :id", "Order found successfully": "Order found successfully", "Order information": "Order information", "Order is created from checkout page": "Order is created from checkout page", "Order is created from the checkout page": "Order is created from the checkout page", "Order not found": "Order not found", "Order notes": "Order notes", "Order number": "Order number", "Order returned successfully": "Order returned successfully", "Order status": "Order status", "Order successfully at :site_title": "Order successfully at :site_title", "Order successfully. Order number :id": "Order successfully. Order number :id", "Order tracking": "Order tracking", "Order tracking :code": "Order tracking :code", "Order tracking is not enabled": "Order tracking is not enabled", "Order was cancelled by customer :customer": "Order was cancelled by customer :customer", "Order was cancelled by customer :customer with reason :reason": "Order was cancelled by customer :customer with reason :reason", "Order was confirmed delivery by customer :customer": "Order was confirmed delivery by customer :customer", "Order was created from checkout page": "Order was created from checkout page", "Ordered at": "Ordered at", "Orders": "Orders", "Other": "Other", "Our Stores": "Our Stores", "Out Of Stock": "Out Of Stock", "Out of Stock": "Out of Stock", "Out of stock": "Out of stock", "Over :fromPrice": "Over :fromPrice", "Overview": "Overview", "Owner": "Owner", "PHP version :version required": "PHP version :version required", "Page could not be found": "Page could not be found", "Page not found": "Page not found", "Password": "Password", "Password confirmation": "Password confirmation", "PayPal ID": "PayPal ID", "PayPal ID is not set!": "PayPal ID is not set!", "PayPal automatically payout": "PayPal automatically payout", "PayPal payout info": "PayPal payout info", "Payment Method": "Payment Method", "Payment Proof": "Payment Proof", "Payment Type": "Payment Type", "Payment description": "Payment description", "Payment failed!": "Payment failed!", "Payment failed! Missing transaction ID.": "Payment failed! Missing transaction ID.", "Payment failed! Something wrong with your payment. Please try again.": "Payment failed! Something wrong with your payment. Please try again.", "Payment failed! Status: :status": "Payment failed! Status: :status", "Payment method": "Payment method", "Payment proof upload is currently disabled.": "Payment proof upload is currently disabled.", "Payment status": "Payment status", "Payment with :paymentType": "Payment with :paymentType", "Payout account": "Payout account", "Payout info": "Payout info", "Payout method is not accepted!": "Payout method is not accepted!", "Permanently delete your account and all associated data.": "Permanently delete your account and all associated data.", "Phone": "Phone", "Phone (optional)": "Phone (optional)", "Phone Number": "Phone Number", "Phone is required": "Phone is required", "Phone label": "Phone label", "Phone number": "Phone number", "Phone:": "Phone:", "Pinterest": "Pinterest", "Please <a href=\":link\">login</a> to write review!": "Please <a href=\":link\">login</a> to write review!", "Please agree to the terms and conditions before proceeding.": "Please agree to the terms and conditions before proceeding.", "Please enter a valid number for quantity": "Please enter a valid number for quantity", "Please enter the quantity you want to buy": "Please enter the quantity you want to buy", "Please enter your CSS code on a single line. It wont work if it has break line. Some special characters may be escaped.": "Please enter your CSS code on a single line. It wont work if it has break line. Some special characters may be escaped.", "Please fill out all shipping information to view available shipping methods!": "Please fill out all shipping information to view available shipping methods!", "Please provide a reason for the cancellation.": "Please provide a reason for the cancellation.", "Please purchase the product for a review!": "Please purchase the product for a review!", "Please select a product to add to cart": "Please select a product to add to cart", "Please select at least 1 product to return!": "Please select at least 1 product to return!", "Please select attributes": "Please select attributes", "Please select product options": "Please select product options", "Please select product options!": "Please select product options!", "Please solve the following math function: :label = ?": "Please solve the following math function: :label = ?", "Please switch currency to any supported currency": "Please switch currency to any supported currency", "Please try again in a few minutes, or alternatively return to the homepage by <a href=\":link\">clicking here</a>.": "Please try again in a few minutes, or alternatively return to the homepage by <a href=\":link\">clicking here</a>.", "Please wait for the administrator to review and approve!": "Please wait for the administrator to review and approve!", "Plus": "Plus", "Popular": "Popular", "Popular tags": "Popular tags", "Popup Delay (seconds)": "<PERSON><PERSON> (seconds)", "Popup Description": "Popup Description", "Popup Image": "Popup Image", "Popup Subtitle": "Popup Subtitle", "Popup Title": "Popup Title", "Post type": "Post type", "Preferences": "Preferences", "Preloader Version": "Preloader Version", "Preloader icon": "Preloader icon", "Preloader icon (optional)": "Preloader icon (optional)", "Price": "Price", "Price: high to low": "Price: high to low", "Price: low to high": "Price: low to high", "Primary": "Primary", "Primary color": "Primary color", "Print invoice": "Print invoice", "Proceed to Checkout": "Proceed to Checkout", "Process payout": "Process payout", "Processed PayPal payout successfully!": "Processed PayPal payout successfully!", "Processing. Please wait...": "Processing. Please wait...", "Product": "Product", "Product :product is out of stock!": "Product :product is out of stock!", "Product :product limited quantity allowed is :quantity": "Product :product limited quantity allowed is :quantity", "Product Categories": "Product Categories", "Product FAQs": "Product FAQs", "Product ID is required": "Product ID is required", "Product Reviews": "Product Reviews", "Product Specification": "Product Specification", "Product categories": "Product categories", "Product detail info": "Product detail info", "Product details sidebar": "Product details sidebar", "Product gallery image style": "Product gallery image style", "Product gallery video position": "Product gallery video position", "Product is not published yet.": "Product is not published yet.", "Product is out of stock": "Product is out of stock", "Product item layout": "Product item layout", "Product item style": "Product item style", "Product listing review style": "Product listing review style", "Product name \":name\" does not exists": "Product name \":name\" does not exists", "Product not found": "Product not found", "Product not found in compare list": "Product not found in compare list", "Product not found in wishlist": "Product not found in wishlist", "Product(s)": "Product(s)", "Products": "Products", "Products by brand bottom sidebar": "Products by brand bottom sidebar", "Products by brand top sidebar": "Products by brand top sidebar", "Products by category bottom sidebar": "Products by category bottom sidebar", "Products by category top sidebar": "Products by category top sidebar", "Products by tag bottom sidebar": "Products by tag bottom sidebar", "Products by tag top sidebar": "Products by tag top sidebar", "Products listing bottom sidebar": "Products listing bottom sidebar", "Products listing page layout": "Products listing page layout", "Products listing top sidebar": "Products listing top sidebar", "Products you have reviewed": "Products you have reviewed", "Profile": "Profile", "Profile Information": "Profile Information", "Promotion": "Promotion", "Promotion discount amount": "Promotion discount amount", "Public Key": "Public Key", "Quantity": "Quantity", "Quantity is required!": "Quantity is required!", "Quantity must be a number!": "Quantity must be a number!", "Quick Actions": "Quick Actions", "Quick Shop": "Quick Shop", "Quick View": "Quick View", "Quick view": "Quick view", "Rate this product:": "Rate this product:", "Rating": "Rating", "Rating: high to low": "Rating: high to low", "Rating: low to high": "Rating: low to high", "Read More": "Read More", "Ready to start shopping?": "Ready to start shopping?", "Reason": "Reason", "Reason (optional)": "Reason (optional)", "Recent": "Recent", "Recent Orders": "Recent Orders", "Recent Posts": "Recent Posts", "Redirecting to Razorpay...": "Redirecting to Razorpay...", "Refund amount": "Refund amount", "Register": "Register", "Register an account": "Register an account", "Register an account on :name": "Register an account on :name", "Register an account with above information?": "Register an account with above information?", "Register as": "Register as", "Register now": "Register now", "Registered successfully!": "Registered successfully!", "Related Articles": "Related Articles", "Related Products": "Related Products", "Related products": "Related products", "Remember me": "Remember me", "Remove": "Remove", "Remove From Compare": "Remove From Compare", "Remove From Wishlist": "Remove From Wishlist", "Remove image": "Remove image", "Remove this item": "Remove this item", "Removed coupon :code successfully!": "Removed coupon :code successfully!", "Removed coupon code successfully!": "Removed coupon code successfully!", "Removed item from cart successfully!": "Removed item from cart successfully!", "Removed product :product from compare list successfully!": "Removed product :product from compare list successfully!", "Removed product :product from wishlist successfully!": "Removed product :product from wishlist successfully!", "Request": "Request", "Request Return Product(s)": "Request Return Product(s)", "Request Return Product(s) In Order :id": "Request Return Product(s) In Order :id", "Request number": "Request number", "Request return order with reason: :reason": "Request return order with reason: :reason", "Requires company invoice (Please fill in your company information to receive the invoice)?": "Requires company invoice (Please fill in your company information to receive the invoice)?", "Reset Password": "Reset Password", "Return Product(s)": "Return Product(s)", "Return Reason": "Return Reason", "Return items": "Return items", "Returned Goods": "Returned Goods", "Returned to Sender": "Returned to <PERSON>er", "Revenue": "Revenue", "Revenues": "Revenues", "Revenues in :label": "Revenues in :label", "Review": "Review", "Review product \":product\"": "Review product \":product\"", "Reviews": "Reviews", "Reviews (:count)": "Reviews (:count)", "Reviews :number": "Reviews :number", "Reviews feature requires Ecommerce plugin": "Reviews feature requires Ecommerce plugin", "Right margin in LTR, left margin in RTL": "Right margin in LTR, left margin in RTL", "Right sidebar": "Right sidebar", "Run": "Run", "SKU": "SKU", "SKU:": "SKU:", "Sale 20% off all store": "Sale 20% off all store", "Sales Reports": "Sales Reports", "Same as shipping information": "Same as shipping information", "Same fee :amount": "Same fee :amount", "Save settings": "Save settings", "Search": "Search", "Search blog posts": "Search blog posts", "Search for Products...": "Search for Products...", "Search result for \":query\"": "Search result for \":query\"", "Search result for: \":query\"": "Search result for: \":query\"", "Search...": "Search...", "Secondary color": "Secondary color", "Secret": "Secret", "Secret Key": "Secret Key", "Secs": "Secs", "Select :name": "Select :name", "Select Coupon": "Select Coupon", "Select Options": "Select Options", "Select a flash sale": "Select a flash sale", "Select ads to show at the left of slider": "Select ads to show at the left of slider", "Select an option": "Select an option", "Select available addresses": "Select available addresses", "Select billing address...": "Select billing address...", "Select categories": "Select categories", "Select city...": "Select city...", "Select country...": "Select country...", "Select file": "Select file", "Select from list (old style)": "Select from list (old style)", "Select individual testimonials with custom stars": "Select individual testimonials with custom stars", "Select state...": "Select state...", "Selection Type": "Selection Type", "Send": "Send", "Send Password Reset Link": "Send Password Reset Link", "Send message": "Send message", "Send message successfully!": "Send message successfully!", "Sent at": "Sent at", "Set the delay time to show the popup after the page is loaded. Set 0 to show the popup immediately.": "Set the delay time to show the popup after the page is loaded. Set 0 to show the popup immediately.", "Set the height of the logo in pixels. The default value is :default.": "Set the height of the logo in pixels. The default value is :default.", "Settings": "Settings", "Setup license code": "Setup license code", "Shape :number": "Shape :number", "Shape image :number": "Shape image :number", "Shape image left": "Shape image left", "Shape image right": "Shape image right", "Share on :social": "Share on :social", "Share your experience with these products": "Share your experience with these products", "Share:": "Share:", "Shipment status": "Shipment status", "Shipments": "Shipments", "Shipping Address": "Shipping Address", "Shipping Company Name": "Shipping Company Name", "Shipping Information": "Shipping Information", "Shipping Label Created": "Shipping Label Created", "Shipping Status": "Shipping Status", "Shipping fee": "Shipping fee", "Shipping information": "Shipping information", "Shipping method": "Shipping method", "Shop Name": "Shop Name", "Shop Name is required.": "Shop Name is required.", "Shop Now": "Shop Now", "Shop Phone": "Shop Phone", "Shop Phone is required.": "Shop Phone is required.", "Shop URL": "Shop URL", "Shop URL is existing. Please choose another one!": "Shop URL is existing. Please choose another one!", "Shop URL is required.": "Shop URL is required.", "Shopping Cart": "Shopping Cart", "Shopping cart": "Shopping cart", "Show": "Show", "Show brand name": "Show brand name", "Show contact form": "Show contact form", "Show featured products only?": "Show featured products only?", "Show menu text": "Show menu text", "Show only discounted products": "Show only discounted products", "Show products count?": "Show products count?", "Show social info": "Show social info", "Show social links": "Show social links", "Showing :from - :to of :total products": "Showing :from - :to of :total products", "Showing :from to :to of :total results": "Showing :from to :to of :total results", "Showing :from-:to of :total stores": "Showing :from-:to of :total stores", "Simple": "Simple", "Simple Text": "Simple Text", "Site Accepted Payments": "Site Accepted Payments", "Site Contact": "Site Contact", "Site Copyright": "Site Copyright", "Site Features": "Site Features", "Site information": "Site information", "Slider": "Slide<PERSON>", "Slider full width": "Slider full width", "Slides to show (if style is slider)": "Slides to show (if style is slider)", "Slug": "Slug", "Social": "Social", "Social Links": "Social Links", "Social Media": "Social Media", "Social Sharing": "Social Sharing", "Social info icon": "Social info icon", "Social info label": "Social info label", "Social sharing buttons": "Social sharing buttons", "Sold by": "Sold by", "Something is broken. Please let us know what you were doing when this error occurred. We will fix it as soon as possible. Sorry for any inconvenience caused.": "Something is broken. Please let us know what you were doing when this error occurred. We will fix it as soon as possible. Sorry for any inconvenience caused.", "Something went wrong.": "Something went wrong.", "Something went wrong. Please try again": "Something went wrong. Please try again", "Sorry, we are doing some maintenance. Please check back soon.": "Sorry, we are doing some maintenance. Please check back soon.", "Sorry, you can only order a maximum of :quantity units of :product at a time. Please adjust the quantity and try again.": "Sorry, you can only order a maximum of :quantity units of :product at a time. Please adjust the quantity and try again.", "Sorry, you cannot buy more than 99,999 items at once": "Sorry, you cannot buy more than 99,999 items at once", "Specification": "Specification", "Specification Attributes": "Specification Attributes", "Specification Groups": "Specification Groups", "Specification Tables": "Specification Tables", "Specify products": "Specify products", "Square Logo": "Square Logo", "Star": "Star", "Stars": "Stars", "Start Shopping": "Start Shopping", "Start shopping now": "Start shopping now", "State": "State", "Status": "Status", "Stop on the last slide": "Stop on the last slide", "Store": "Store", "Store ID": "Store ID", "Store Information": "Store Information", "Store Name": "Store Name", "Store Password (API/Secret key)": "Store Password (API/Secret key)", "Store Performance": "Store Performance", "Store Settings": "Store Settings", "Store URL": "Store URL", "Store Visibility": "Store Visibility", "Stores": "Stores", "Style": "Style", "Style :number": "Style :number", "Styles": "Styles", "Sub amount": "Sub amount", "Subject": "Subject", "Submit": "Submit", "Submit Return Request": "Submit Return Request", "Submit Review": "Submit Review", "Subscribe": "Subscribe", "Subscribe our Newsletter": "Subscribe our Newsletter", "Subscribe to newsletter successfully!": "Subscribe to newsletter successfully!", "Subtitle": "Subtitle", "Subtotal": "Subtotal", "Subtotal:": "Subtotal:", "Success": "Success", "Support native audio": "Support native audio", "Support native video, YouTube, Vimeo, TikTok, X (Twitter)": "Support native video, YouTube, Vimeo, TikTok, X (Twitter)", "Tab #:number": "Tab #:number", "Tablet Image": "Tablet Image", "Tabs": "Tabs", "Tag:": "Tag:", "Tags": "Tags", "Tags:": "Tags:", "Take me home": "Take me home", "Tax": "Tax", "Tax ID": "Tax ID", "Tax ID:": "Tax ID:", "Tax info": "Tax info", "Tax information": "Tax information", "Tax:": "Tax:", "Telegram": "Telegram", "Tell us why you want to delete your account...": "Tell us why you want to delete your account...", "Temporarily down for maintenance": "Temporarily down for maintenance", "Term and Policy": "Term and Policy", "Terms and Privacy Policy": "Terms and Privacy Policy", "Testimonial": "Testimonial", "Testimonials": "Testimonials", "Text color (optional)": "Text color (optional)", "Thank you for purchasing our products!": "Thank you for purchasing our products!", "The .env file is not writable.": "The .env file is not writable.", "The balance is not enough for withdrawal": "The balance is not enough for withdrawal", "The debug mode has been disabled successfully.": "The debug mode has been disabled successfully.", "The debug mode is already disabled.": "The debug mode is already disabled.", "The font size in pixels (px). Default is :default": "The font size in pixels (px). Default is :default", "The given email address has not been confirmed. <a href=\":resend_link\">Resend confirmation link.</a>": "The given email address has not been confirmed. <a href=\":resend_link\">Resend confirmation link.</a>", "The minimum withdrawal amount is :amount": "The minimum withdrawal amount is :amount", "The order could not be found. Please try again or contact us if you need assistance.": "The order could not be found. Please try again or contact us if you need assistance.", "The order is currently being processed. For expedited processing, kindly upload a copy of your payment proof:": "The order is currently being processed. For expedited processing, kindly upload a copy of your payment proof:", "The page you are looking for could not be found.": "The page you are looking for could not be found.", "The selected :attribute is invalid.": "The selected :attribute is invalid.", "The system is up-to-date. There are no new versions to update!": "The system is up-to-date. There are no new versions to update!", "The total amount (including fee) exceeds your current balance": "The total amount (including fee) exceeds your current balance", "Theme built-in": "Theme built-in", "Theme options": "Theme options", "Then you need to create a new webhook. To create a webhook, go to <strong>Account Settings</strong>-><strong>API keys</strong>-><strong>Webhooks</strong> and paste the below url to <strong>Webhook URL</strong> field.": "Then you need to create a new webhook. To create a webhook, go to <strong>Account Set<PERSON>s</strong>-><strong>API keys</strong>-><strong>Webhooks</strong> and paste the below url to <strong>Webhook URL</strong> field.", "There is no data to display!": "There is no data to display!", "This action cannot be undone": "This action cannot be undone", "This action will permanently delete your account and all associated data and is irreversible. Please be sure before proceeding.": "This action will permanently delete your account and all associated data and is irreversible. Please be sure before proceeding.", "This color may be overridden by the theme. If it doesnt work, please add your CSS in Appearance -> Custom CSS.": "This color may be overridden by the theme. If it doesnt work, please add your CSS in Appearance -> Custom CSS.", "This credential is invalid Google Analytics credentials.": "This credential is invalid Google Analytics credentials.", "This feature is temporary disabled in demo mode. Please use another login option. Such as Google.": "This feature is temporary disabled in demo mode. Please use another login option. Such as Google.", "This file is not a valid JSON file.": "This file is not a valid JSON file.", "This image will be used as placeholder for lazy load images. The best size for this image is 250x250px.": "This image will be used as placeholder for lazy load images. The best size for this image is 250x250px.", "This logo will be used in some special cases. Such as checkout page.": "This logo will be used in some special cases. Such as checkout page.", "This option is only applied for header style 1": "This option is only applied for header style 1", "This product is not available.": "This product is not available.", "This product is out of stock or not exists!": "This product is out of stock or not exists!", "Time": "Time", "Title": "Title", "Title font size (px)": "Title font size (px)", "Toggle to display or hide social links on your site. Configure the links in Theme Options -> Social Links.": "Toggle to display or hide social links on your site. Configure the links in Theme Options -> Social Links.", "Top": "Top", "Top Rated": "Top Rated", "Top Selling Products": "Top Selling Products", "Top rated": "Top rated", "Total": "Total", "Total Amount": "Total Amount", "Total amount": "Total amount", "Total:": "Total:", "Track": "Track", "Track your recent orders and order history": "Track your recent orders and order history", "Tracking ID": "Tracking ID", "Tracking Link": "Tracking Link", "Transaction ID": "Transaction ID", "Transaction is already successfully completed!": "Transaction is already successfully completed!", "Transaction is successfully completed!": "Transaction is successfully completed!", "Trending": "Trending", "Trending Products": "Trending Products", "Type": "Type", "Type your message...": "Type your message...", "UPI ID": "UPI ID", "URL": "URL", "Unable to download files": "Unable to download files", "Unable to set debug mode. No APP_DEBUG variable was found in the .env file.": "Unable to set debug mode. No APP_DEBUG variable was found in the .env file.", "Unknown": "Unknown", "Unsubscribe to newsletter successfully": "Unsubscribe to newsletter successfully", "Update": "Update", "Update :name": "Update :name", "Update cart successfully!": "Update cart successfully!", "Update profile successfully!": "Update profile successfully!", "Update return order status to: :status": "Update return order status to: :status", "Update successfully!": "Update successfully!", "Update withdrawal request #:id": "Update withdrawal request #:id", "Update your account profile information and email address.": "Update your account profile information and email address.", "Update your shipping and billing addresses": "Update your shipping and billing addresses", "Updated avatar successfully!": "Updated avatar successfully!", "Updated registration info successfully!": "Updated registration info successfully!", "Upload": "Upload", "Upload Service Account JSON File": "Upload Service Account JSON File", "Upload photos": "Upload photos", "Uploaded Certificate": "Uploaded Certificate", "Uploaded Government ID": "Uploaded Government ID", "Uploaded proof successfully": "Uploaded proof successfully", "Use air paper icon with animation": "Use air paper icon with animation", "Use shape images": "Use shape images", "Use this address as default.": "Use this address as default.", "Using coupon code": "Using coupon code", "Validation Fail!": "Validation Fail!", "Variables": "Variables", "Vendor": "<PERSON><PERSON><PERSON>", "Vendor Dashboard": "Vendor Dashboard", "Vendor account is not verified.": "Vendor account is not verified.", "Vendor:": "Vendor:", "Vertical": "Vertical", "View": "View", "View All Products": "View All Products", "View Cart": "View Cart", "View Certificate": "View Certificate", "View Details": "View Details", "View Full Orders": "View Full Orders", "View Full Products": "View Full Products", "View Government ID": "View Government ID", "View Orders": "View Orders", "View Receipt:": "View Receipt:", "View all results": "View all results", "View full details": "View full details", "View more": "View more", "View withdrawal request #:id": "View withdrawal request #:id", "View your store": "View your store", "View your store <a href=\":url\">here</a>": "View your store <a href=\":url\">here</a>", "Viewing message #:id": "Viewing message #:id", "Visit Store": "Visit Store", "Waiting for approval": "Waiting for approval", "Waiting for your review": "Waiting for your review", "Warning": "Warning", "Warning: This product is on backorder and may take longer to ship.": "Warning: This product is on backorder and may take longer to ship.", "We have sent you an email to verify your email. Please check and confirm your email address!": "We have sent you an email to verify your email. Please check and confirm your email address!", "We sent you another confirmation email. You should receive it shortly.": "We sent you another confirmation email. You should receive it shortly.", "We will send you an email to confirm your account deletion. Once you confirm, your account will be deleted permanently and all your data will be lost.": "We will send you an email to confirm your account deletion. Once you confirm, your account will be deleted permanently and all your data will be lost.", "Webhook Secret": "Webhook Secret", "Welcome back, <strong>:name</strong>!": "Welcome back, <strong>:name</strong>!", "WhatsApp": "WhatsApp", "When a payment fails": "When a payment fails", "When a payment is authorized": "When a payment is authorized", "When a payment is captured": "When a payment is captured", "When a payment is pending": "When a payment is pending", "When an order is paid": "When an order is paid", "When enabled, shortcode content will be loaded sequentially as the page loads, rather than all at once. This can help improve page load times.": "When enabled, shortcode content will be loaded sequentially as the page loads, rather than all at once. This can help improve page load times.", "Whoops, this is embarrassing. Looks like the page you were looking for wasn't found.": "Whoops, this is embarrassing. Looks like the page you were looking for wasn't found.", "Widget display blog categories": "Widget display blog categories", "Widget display site information": "Widget display site information", "Width": "<PERSON><PERSON><PERSON>", "Wishlist": "Wishlist", "Withdraw": "Withdraw", "Withdrawal": "<PERSON><PERSON><PERSON>", "Withdrawal images": "Withdrawal images", "Withdrawal request": "<PERSON><PERSON><PERSON> request", "Withdrawals": "<PERSON><PERSON><PERSON><PERSON>", "Without layout": "Without layout", "Without title": "Without title", "Write your review": "Write your review", "X (Twitter)": "X (Twitter)", "Yes": "Yes", "Yes, turn off": "Yes, turn off", "You can change it <a href=\":link\">here</a>": "You can change it <a href=\":link\">here</a>", "You can create your app in :link": "You can create your app in :link", "You can get fan page ID using this site :link": "You can get fan page ID using this site :link", "You can now download it by clicking the links below": "You can now download it by clicking the links below", "You can only add products from the same store to the cart.": "You can only add products from the same store to the cart.", "You can upload the following file types: jpg, jpeg, png, pdf and max file size is 2MB.": "You can upload the following file types: jpg, jpeg, png, pdf and max file size is 2MB.", "You can upload up to :total photos, each photo maximum size is :max MB.": "You can upload up to :total photos, each photo maximum size is :max MB.", "You can upload up to :total photos, each photo maximum size is :max kilobytes": "You can upload up to :total photos, each photo maximum size is :max kilobytes", "You cannot cancel this order": "You cannot cancel this order", "You cannot confirm delivery for this order": "You cannot confirm delivery for this order", "You cannot return this order": "You cannot return this order", "You cannot send a message to your own store.": "You cannot send a message to your own store.", "You do not have permission to delete this review.": "You do not have permission to delete this review.", "You have :total product(s) but no orders yet": "You have :total product(s) but no orders yet", "You have a coupon code?": "You have a coupon code?", "You have created a payment #:charge_id via :channel :time : :amount": "You have created a payment #:charge_id via :channel :time : :amount", "You have money!": "You have money!", "You have not placed any order return requests yet.": "You have not placed any order return requests yet.", "You have not placed any orders yet.": "You have not placed any orders yet.", "You have not purchased any digital products yet.": "You have not purchased any digital products yet.", "You have recovered from previous orders!": "You have recovered from previous orders!", "You have reviewed this product already!": "You have reviewed this product already!", "You have uploaded a copy of your payment proof.": "You have uploaded a copy of your payment proof.", "You haven't placed any orders yet. Browse our products and find something you love!": "You haven't placed any orders yet. Browse our products and find something you love!", "You must agree to the terms and conditions and privacy policy.": "You must agree to the terms and conditions and privacy policy.", "You must agree to the terms and policy.": "You must agree to the terms and policy.", "You must buy at least 1 item": "You must buy at least 1 item", "You need to add :quantity more items of :product to place your order.": "You need to add :quantity more items of :product to place your order.", "You need to add :quantity more items to place your order.": "You need to add :quantity more items to place your order. ", "You received a payment. Thanks for being an affiliate on our site!": "You received a payment. Thanks for being an affiliate on our site!", "You received a payment. Thanks for selling on our site!": "You received a payment. Thanks for selling on our site!", "You successfully confirmed your email address.": "You successfully confirmed your email address.", "You will be redirected to :name to complete the payment.": "You will be redirected to :name to complete the payment.", "You will receive money through the information below": "You will receive money through the information below", "YouTube URL": "YouTube URL", "YouTube video": "YouTube video", "YouTube, Vimeo, TikTok, ...": "YouTube, <PERSON><PERSON>o, Tik<PERSON>ok, ...", "Your Address": "Your Address", "Your Addresses": "Your Addresses", "Your Cart is empty": "Your Cart is empty", "Your Email": "Your Email", "Your Message": "Your Message", "Your Name": "Your Name", "Your Phone": "Your Phone", "Your Review": "Your Review", "Your Reviews": "Your Reviews", "Your account has been locked, please contact the administrator.": "Your account has been locked, please contact the administrator.", "Your asset files have been published successfully.": "Your asset files have been published successfully.", "Your cart is empty": "Your cart is empty", "Your compare list is empty": "Your compare list is empty", "Your email": "Your email", "Your email address": "Your email address", "Your email address will not be published. Required fields are marked *": "Your email address will not be published. Required fields are marked *", "Your email does not exist in the system or you have unsubscribed already!": "Your email does not exist in the system or you have unsubscribed already!", "Your email is in blacklist. Please use another email address.": "Your email is in blacklist. Please use another email address.", "Your full name": "Your full name", "Your message contains blacklist words: \":words\".": "Your message contains blacklist words: \":words\".", "Your name": "Your name", "Your order is successfully placed": "Your order is successfully placed", "Your personal data will be used to support your experience throughout this website, to manage access to your account.": "Your personal data will be used to support your experience throughout this website, to manage access to your account.", "Your rating:": "Your rating:", "Your shopping cart has digital product(s), so you need to sign in to continue!": "Your shopping cart has digital product(s), so you need to sign in to continue!", "Your system has been cleaned up successfully.": "Your system has been cleaned up successfully.", "Your wishlist is empty": "Your wishlist is empty", "Your wishlist list is empty": "Your wishlist list is empty", "Zip code": "Zip code", "Zipcode": "Zipcode", "billion": "billion", "centimeters": "centimeters", "error": "error", "for all orders": "for all orders", "for all product in collection :collections": "for all product in collection :collections", "for all products in category :categories": "for all products in category :categories", "for all products in collection :collections": "for all products in collection :collections", "for all products in order": "for all products in order", "for customer(s) :customers": "for customer(s) :customers", "for order with amount from :price": "for order with amount from :price", "for product variant(s) :variants": "for product variant(s) :variants", "for product(s) :products": "for product(s) :products", "grams": "grams", "here": "here", "item(s)": "item(s)", "kilograms": "kilograms", "limited to use coupon code per customer. This coupon can only be used once per customer!": "limited to use coupon code per customer. This coupon can only be used once per customer!", "meters": "meters", "million": "million", "times": "times", "when shipping fee less than or equal :amount": "when shipping fee less than or equal :amount", "| (pipe)": "| (pipe)", "✅ Purchased :time": "✅ Purchased :time"}