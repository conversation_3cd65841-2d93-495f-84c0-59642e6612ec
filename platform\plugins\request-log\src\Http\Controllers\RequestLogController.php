<?php

namespace Bo<PERSON>ble\RequestLog\Http\Controllers;

use Bo<PERSON>ble\Base\Facades\Assets;
use Bo<PERSON>ble\Base\Http\Actions\DeleteResourceAction;
use Botble\Base\Http\Controllers\BaseSystemController;
use Bo<PERSON>ble\RequestLog\Models\RequestLog;
use Bo<PERSON>ble\RequestLog\Tables\RequestLogTable;
use Illuminate\Http\Request;

class RequestLogController extends BaseSystemController
{
    public function getWidgetRequestErrors(Request $request)
    {
        $limit = $request->integer('paginate', 10);
        $limit = $limit > 0 ? $limit : 10;

        $requests = RequestLog::query()->latest()
            ->paginate($limit);

        return $this
            ->httpResponse()
            ->setData(view('plugins/request-log::widgets.request-errors', compact('requests', 'limit'))->render());
    }

    public function index(RequestLogTable $dataTable)
    {
        Assets::addScriptsDirectly('vendor/core/plugins/request-log/js/request-log.js');

        $this->pageTitle(trans('plugins/request-log::request-log.name'));

        return $dataTable->renderTable();
    }

    public function destroy(RequestLog $requestLog)
    {
        return DeleteResourceAction::make($requestLog);
    }

    public function deleteAll()
    {
        RequestLog::query()->truncate();

        return $this
            ->httpResponse()
            ->withDeletedSuccessMessage();
    }
}
