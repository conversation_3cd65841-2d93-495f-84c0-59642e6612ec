<section class="tp-product-area position-relative pt-30 pb-30">
    <div class="container">
        <div class="row align-items-center mb-40">
            <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['col-xl-5 col-lg-6 col-md-5' => count($selectedTabs) > 1, 'col-12' => count($selectedTabs) <= 1]); ?>">
                <?php echo Theme::partial('section-title', compact('shortcode')); ?>

            </div>
            <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['col-xl-7 col-lg-6 col-md-7' => count($selectedTabs) > 1, 'd-none' => count($selectedTabs) <= 1]); ?>">
                <div class="tp-product-tab tp-product-tab-border tp-tab d-flex justify-content-md-end">
                    <ul
                        class="nav nav-tabs justify-content-sm-end"
                        id="productTab"
                        role="tablist"
                        data-ajax-url="<?php echo e(route('public.ajax.products', ['limit' => $shortcode->limit ?: 8])); ?>"
                    >
                        <?php $__currentLoopData = $productTabs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $tab): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(! in_array($key, $selectedTabs) || (! EcommerceHelper::isReviewEnabled() && $key === 'top-rated')) continue; ?>

                            <li class="nav-item" role="presentation">
                                <button
                                    class="<?php echo \Illuminate\Support\Arr::toCssClasses(['nav-link', 'active' => $loop->first]); ?>"
                                    id="<?php echo e($key); ?>-tab"
                                    data-bs-toggle="tab"
                                    data-bs-target="#tab-pane"
                                    type="button"
                                    role="tab"
                                    aria-controls="tab-pane"
                                    <?php if($loop->first): ?> aria-selected="true" <?php endif; ?>
                                    data-bb-toggle="product-tab"
                                    data-bb-value="<?php echo e($key); ?>"
                                >
                                    <?php echo e($tab); ?>

                                    <span class="tp-product-tab-line">
                                        <?php echo Theme::partial('section-title-shape'); ?>

                                    </span>
                                </button>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div class="tp-product-tab-content">
                    <div class="tab-content" id="productTabContent">
                        <div class="tab-pane fade show active" id="tab-pane" role="tabpanel" aria-labelledby="tab" tabindex="0">
                            <?php $__currentLoopData = $groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $tab): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if(! isset($tab['products'])) continue; ?>

                                <?php echo $__env->make(
                                    Theme::getThemeNamespace('views.ecommerce.includes.product-items'),
                                    ['products' => $tab['products'], 'itemsPerRow' => get_products_per_row(), 'layout' => 'grid']
                                , array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/partials/shortcodes/ecommerce-product-groups/tabs.blade.php ENDPATH**/ ?>