/* Profile Off-canvas Menu Styles */
@use '../utils' as *;
.profile__tab-hamburger {
    display: none;

    @media #{$md, $sm, $xs} {
        display: block;
    }

    .profile-menu-toggle {
        display: flex;
        align-items: center;
        background-color: var(--tp-theme-primary);
        color: var(--tp-common-white);
        border: none;
        padding: 10px 18px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
        box-shadow: 0 4px 10px rgba(var(--tp-theme-primary-rgb), 0.2);

        svg {
            margin-right: 5px;
        }

        &:hover {
            background-color: var(--tp-theme-secondary);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(var(--tp-theme-primary-rgb), 0.3);
        }

        &:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(var(--tp-theme-primary-rgb), 0.2);
        }
    }
}

.profile-menu-panel {
    position: fixed;
    inset-inline-start: 0;
    top: 0;
    width: 320px;
    height: 100%;
    @include transform(translateX(calc(-100%)));
    background: var(--tp-common-white) none repeat scroll 0 0;
    @include tp-transition-mul((all 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86)));
    z-index: 99999;
    overflow-y: scroll;
    overscroll-behavior-y: contain;
    scrollbar-width: none;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);

    &::-webkit-scrollbar {
        display: none; /* for Chrome, Safari, and Opera */
    }

    &.profile-menu-panel-opened {
        @include transform(translateX(0));
        opacity: 1;
    }

    &__wrapper {
        position: relative;
        padding: 25px 0 50px;
        z-index: 1;
        min-height: 100%;
    }

    &__close {
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 10;

        &-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            height: 36px;
            width: 36px;
            border-radius: 50%;
            background-color: #f5f5f5;
            color: var(--tp-common-black);
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;

            svg {
                width: 16px;
                height: 16px;
                stroke-width: 2;
            }

            &:hover {
                background-color: var(--tp-theme-primary);
                color: var(--tp-common-white);
                transform: rotate(90deg);
            }
        }
    }

    &__content {
        padding-top: 15px;
    }

    &__top {
        padding: 0 20px;
    }

    .mobile-profile-menu {
        padding: 0;

        .profile-user-info {
            padding: 0 20px 20px;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);

            .profile-user-avatar {
                width: 70px;
                height: 70px;
                border-radius: 50%;
                overflow: hidden;
                margin-bottom: 15px;
                border: 2px solid var(--tp-theme-primary);

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .profile-user-name {
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 5px;
                color: var(--tp-common-black);
            }

            .profile-user-email {
                font-size: 14px;
                color: var(--tp-text-body);
                margin-bottom: 0;
            }
        }

        .profile-menu-list {
            padding: 0 10px;

            .nav-link {
                display: flex;
                align-items: center;
                padding: 14px 15px;
                color: var(--tp-common-black);
                font-weight: 500;
                border-radius: 8px;
                margin-bottom: 8px;
                transition: all 0.3s ease;

                &:hover {
                    background-color: rgba(var(--tp-theme-primary-rgb), 0.08);
                    color: var(--tp-theme-primary);
                    transform: translateX(3px);
                }

                &.active {
                    background-color: var(--tp-theme-primary);
                    color: var(--tp-common-white);
                    box-shadow: 0 4px 10px rgba(var(--tp-theme-primary-rgb), 0.3);

                    span {
                        svg {
                            stroke: var(--tp-common-white);
                        }
                    }
                }

                span {
                    margin-right: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 24px;
                    height: 24px;

                    svg {
                        width: 20px;
                        height: 20px;
                        stroke-width: 1.5;
                    }
                }
            }
        }
    }
}

[dir='rtl'] {
    .profile-menu-panel {
        transform: translateX(100%);
        right: initial;
        left: 0;

        &.profile-menu-panel-opened {
            transform: translateX(0);
        }

        &__close {
            right: auto;
            left: 20px;
        }

        .mobile-profile-menu {
            .profile-menu-list {
                .nav-link {
                    &:hover {
                        transform: translateX(-3px);
                    }

                    span {
                        margin-right: 0;
                        margin-left: 12px;
                    }
                }
            }
        }
    }
}
