$((function(){$(document).on("click",".button-import-groups, .button-re-import",(function(t){t.preventDefault();var o=$(t.currentTarget);$httpClient.make().withButtonLoading(o).postForm(o.data("url")).then((function(t){var e=t.data;if(Botble.showSuccess(e.message),o.closest(".modal").length){o.closest(".modal").modal("hide");var a=$(".translations-table .table");a.length?a.DataTable().ajax.url(window.location.href).load():setTimeout((function(){window.location.reload()}),1e3)}else setTimeout((function(){window.location.reload()}),1e3)}))}))}));