@use '../../utils' as *;
/*----------------------------------------*/
/*  3.3 Header Style 3
/*----------------------------------------*/

.#{$theme-prefix}-header {
    $self: &;
    &-sticky {
        &.header-sticky,
        &.header-main {
            .main-menu.menu-style-3 ul li.has-dropdown > a::after {
                color: var(--tp-common-black);
            }

            .main-menu.menu-style-3 ul li.has-dropdown:hover > a::after {
                color: var(--tp-theme-primary);
            }
            .main-menu.menu-style-3 > nav > ul > li > a {
                color: var(--tp-common-black);
            }

            .main-menu.menu-style-3.menu-style-4 ul li.has-dropdown > a::after {
                color: var(--tp-common-black);
            }
            .main-menu.menu-style-3.menu-style-4 ul li:hover > a::after {
                color: var(--tp-theme-primary);
            }
            .main-menu.menu-style-3 > nav > ul > li:hover > a {
                color: var(--tp-theme-primary);
            }

            & .tp-header-action-item .tp-header-action-btn {
                color: var(--tp-common-black);

                & .tp-header-action-badge {
                    background-color: var(--tp-theme-primary);
                    border-color: var(--tp-theme-primary);
                    color: var(--tp-common-white);
                }

                &:hover {
                    color: var(--tp-theme-primary);
                }
            }
            &.has-dark-logo {
                & .logo {
                    & .logo-light {
                        display: none;
                    }
                    & .logo-dark {
                        display: block;
                    }
                }
            }
        }
    }
    &-style-transparent-white {
        #{$self} {
            &-action {
                &-badge {
                    background-color: var(--tp-common-white);
                    color: var(--tp-common-black);
                    width: 22px;
                    height: 22px;
                    line-height: 22px;
                    border: 0;
                }
                &-item {
                    &:last-child {
                        @media #{$md, $sm, $xs} {
                            margin-right: 0;
                        }
                    }
                }
                &-btn {
                    color: var(--tp-common-white);
                    &:hover {
                        color: var(--tp-common-white);
                    }
                }
            }
            &-top-menu-item {
                & ul {
                    & li {
                        & a {
                            &:hover {
                                color: var(--secondary-color);
                            }
                        }
                    }
                }
            }
        }
    }

    &-bottom-3 {
        border-bottom: 1px solid rgba($color: $white, $alpha: 0.14);
        @media #{$xl, $lg} {
            padding-left: 30px;
            padding-right: 30px;
        }
        @media #{$lg} {
            padding-left: 20px;
            padding-right: 20px;
        }
        @media #{$md} {
            padding-left: 25px;
            padding-right: 25px;
        }
        @media #{$md, $sm, $xs} {
            padding-top: 12px;
            padding-bottom: 12px;
        }

        @media #{$sm, $xs} {
            padding-left: 0;
            padding-right: 0;
        }
    }
}

.has-dark-logo {
    & .logo {
        & .logo-dark {
            display: none;
        }
    }
}

.tp-menu-line {
    position: absolute;
    display: inline-block;
    height: 1px;
    background-color: var(--tp-common-white);
    @extend %tp-transition;
}
