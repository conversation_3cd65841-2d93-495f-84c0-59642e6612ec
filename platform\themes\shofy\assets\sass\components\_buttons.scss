@use '../utils' as *;

/*----------------------------------------*/
/*  2.3 Buttons
/*----------------------------------------*/

.#{$theme-prefix}-btn {
    display: inline-block;
    font-family: var(--tp-ff-jost);
    font-weight: 400;
    font-size: 16px;
    background-color: var(--tp-common-black);
    padding: 9px 26px;
    color: var(--tp-common-white);
    border: 1px solid var(--tp-common-black);

    & svg {
        @extend %tp-svg-y-2;
    }

    &:hover {
        color: var(--tp-common-black);
        background-color: transparent;
    }

    &-2 {
        /* for home electronic */
        font-family: var(--primary-font);
        font-weight: 500;
        font-size: 14px;
        background-color: var(--tp-theme-primary);
        padding: 9px 26px 7px;
        border-radius: 6px;
        border: 0;

        &:hover {
            background-color: var(--tp-common-black);
            color: var(--tp-common-white);
        }
    }

    &-white {
        /* for home electronic */
        background-color: var(--tp-common-white);
        box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.1);
        color: var(--tp-common-black);
        padding: 9px 29px;

        &:hover {
            background-color: var(--tp-common-black);
            color: var(--tp-common-white);
        }
    }

    &-blue {
        /* for home electronic */
        background-color: var(--tp-theme-primary);
    }

    &-border {
        background-color: transparent;
        border: 1.5px solid var(--tp-common-black);
        color: var(--tp-common-black);
        padding: 8px 29px;
        & svg {
            @include transform(translateY(-2px) !important);
        }
        &:hover {
            background-color: var(--tp-theme-primary);
            border-color: var(--tp-theme-primary);
            color: var(--tp-common-white);
        }

        &-sm {
            padding: 5px 25px;
        }
        &-white {
            border-color: rgba($color: $white, $alpha: 0.3);
            color: var(--tp-common-white);
            &:hover {
                color: var(--tp-common-white);
                background-color: var(--tp-common-black);
                border-color: var(--tp-common-black);
            }

            &-sm {
                padding: 4px 26px 6px;
            }
        }
        &-2 {
            border: 2px solid rgba($color: #04092d, $alpha: 0.1);
            font-weight: 500;
            font-size: 13px;
            color: var(--tp-common-black);
            background-color: transparent;
            padding: 8px 17px;
            & span {
                margin-left: 3px;
            }

            & svg {
                @extend %tp-svg-y-1;
            }

            &:hover {
                background-color: var(--tp-theme-primary);
                border-color: var(--tp-theme-primary);
                color: var(--tp-common-white);
            }
        }
    }
}

.#{$theme-prefix}-btn-green {
    font-weight: 400;
    font-size: 16px;
    color: var(--tp-common-white);
    display: inline-block;
    padding: 9px 48px;
    border-radius: 24px;
    background-color: var(--tp-theme-primary);

    &:hover {
        background-color: var(--tp-common-black);
        color: var(--tp-common-white);
    }

    & svg {
        @extend %tp-svg-y-2;
    }

    &-sm {
        padding: 4px 24px;
    }
}

/* offcanvas btn */

.#{$theme-prefix}-btn-offcanvas {
    display: inline-block;
    font-size: 14px;
    font-weight: 600;
    color: var(--tp-common-white);
    background: var(--tp-theme-primary);
    text-align: center;
    font-family: var(--tp-ff-space);
    padding: 9px 22px;
    letter-spacing: -0.02em;
    position: relative;
    z-index: 1;

    & i {
        padding-left: 5px;
    }
    &:hover {
        background-color: var(--tp-common-black);
        color: var(--tp-common-white);
    }
}

.#{$theme-prefix}-pulse-border {
    &::after,
    &::before {
        position: absolute;
        content: '';
        left: 50%;
        top: 50%;
        @include transform(translate(-50%, -50%));
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 1px solid rgba($color: #fff, $alpha: 0.4);
        animation: borderanimate2 2s linear infinite;
        animation-delay: 0s;
        -webkit-animation-delay: 0s;
        animation-delay: 0s;
        @extend %tp-transition;
        z-index: -1;
    }

    &::before {
        animation-delay: 0.7s;
    }
}

.#{$theme-prefix}-link-btn {
    display: inline-block;
    font-weight: 500;
    font-size: 13px;
    color: var(--tp-common-black);
    @include tp-transition-mul(color 0.3s ease-in-out);
    & i,
    & svg {
        @include tp-transition-mul((transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)));
        margin-left: 2px;
    }

    & svg {
        @extend %tp-svg-y-1;
    }
    &:hover {
        color: var(--tp-theme-primary);
        & svg {
            @include transform(translate(3px, -1px));
        }
        & i {
            @include transform(translateX(3px));
        }
    }
    &-2 {
        font-weight: 400;
        font-size: 16px;
        color: var(--tp-common-black);
        position: relative;
        &::after,
        &::before {
            position: absolute;
            content: '';
            left: auto;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 1px;
            background-color: var(--tp-common-black);
            @extend %tp-transition;
        }
        &::before {
            width: 0%;
            background-color: var(--tp-theme-primary);
            z-index: 1;
        }
        &:hover {
            color: var(--tp-theme-primary);
            & svg,
            & i {
                @include transform(translate(3px, -1px));
            }

            &::before {
                left: 0;
                right: auto;
                width: 100%;
                background-color: var(--tp-theme-primary);
            }
        }
    }
}

.#{$theme-prefix}-link-btn-3 {
    font-size: 16px;
    color: var(--tp-common-black);
    & span {
        display: inline-block;
        @include tp-transition-mul((transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)));
        & svg {
            @extend %tp-svg-y-1;
        }
    }

    &:hover {
        color: var(--tp-theme-primary);

        & span {
            @include transform(translateX(3px) translateY(0));
        }
    }
}

/* link btn line */

.#{$theme-prefix}-link-btn-line {
    font-size: 16px;
    line-height: 10px;
    text-transform: uppercase;
    color: var(--tp-common-black);
    position: relative;

    &::after {
        position: absolute;
        content: '';
        left: auto;
        right: 0;
        width: 0%;
        height: 1px;
        bottom: 3px;
        background-color: var(--tp-theme-primary);
        @extend %tp-transition;
    }

    & svg {
        @extend %tp-svg-y-1;
    }

    &:hover {
        color: var(--tp-theme-primary);
        &::after {
            width: 100%;
            left: 0;
            right: auto;
        }
    }
}

.#{$theme-prefix}-link-btn-line-2 {
    font-size: 16px;
    line-height: 10px;
    color: var(--tp-common-black);
    position: relative;

    &::after {
        position: absolute;
        content: '';
        left: auto;
        right: 0;
        width: 100%;
        height: 1px;
        bottom: 2px;
        background-color: var(--tp-common-black);
        @extend %tp-transition;
    }

    & svg {
        @extend %tp-svg-y-1;
    }

    & svg,
    & i {
        @include tp-transition(all, 0.2s);
    }

    &:hover {
        color: var(--tp-theme-primary);
        &::after {
            width: 100%;
            left: 0;
            right: auto;
            background-color: var(--tp-theme-primary);
        }
    }
}

.#{$theme-prefix}-hamburger-btn-2 {
    width: 44px;
    height: 44px;
    background-color: rgba($color: $white, $alpha: 0.14);
    border-radius: 6px;
    position: relative;
    & span {
        position: absolute;
        top: 14px;
        left: 13px;
        display: block;
        width: 16px;
        height: 2px;
        background-color: var(--tp-common-white);
        border-radius: 10px;

        &:nth-child(2) {
            top: 21px;
            width: 20px;
        }
        &:nth-child(3) {
            top: 28px;
        }
    }
}

.#{$theme-prefix}-load-more-btn {
    display: inline-block;
    padding: 5px 33px;
    border: 1px solid var(--tp-common-black);
    font-weight: 400;
    font-size: 16px;
    color: var(--tp-common-black);

    & svg {
        @extend %tp-svg-y-2;
    }

    &:hover {
        color: var(--tp-common-white);
        background-color: var(--tp-theme-primary);
        border-color: var(--tp-theme-primary);
    }
}

.#{$theme-prefix}-filter-btn {
    font-weight: 500;
    font-size: 14px;
    line-height: 1;
    color: var(--tp-common-white);
    background-color: var(--tp-common-black);
    display: inline-block;
    padding: 13px 35px 9px;
    border: 1px solid var(--tp-common-black);
    & svg {
        @extend %tp-svg-y-2;
    }

    &:hover {
        color: var(--tp-common-black);
        background-color: var(--tp-common-white);
    }

    @media #{$md} {
        padding-left: 30px;
        padding-right: 30px;
    }

    @media (max-width: 768px) {
        padding: 13px 20px 9px;
    }
}

.#{$theme-prefix}-product-list-add-to-cart-btn {
    font-weight: 500;
    font-size: 14px;
    color: var(--tp-common-white);
    background-color: var(--tp-common-black);
    display: inline-block;
    padding: 5px 20px;

    &:hover {
        background-color: var(--tp-theme-primary);
        color: var(--tp-common-white);
    }
}

.#{$theme-prefix}-menu-showcase-btn {
    font-weight: 500;
    font-size: 14px;
    padding: 11px 26px 9px;
    border: 0;
    color: var(--tp-common-white);
    background-color: var(--tp-theme-primary);
    &-2 {
        border-radius: 6px;
        font-family: var(--primary-font);
    }

    &:hover {
        background-color: var(--tp-common-black);
        color: var(--tp-common-white);
    }
}
