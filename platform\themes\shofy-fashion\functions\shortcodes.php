<?php

use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\TextField;
use Bo<PERSON>ble\Shortcode\Facades\Shortcode;
use Bo<PERSON>ble\Shortcode\Forms\ShortcodeForm;

app()->booted(function (): void {
    if (is_plugin_active('blog')) {
        Shortcode::modifyAdminConfig('blog-posts', function (ShortcodeForm $form) {
            return $form->addAfter(
                'title',
                'subtitle',
                TextField::class,
                TextFieldOption::make()->label(__('Subtitle'))->colspan(2)
            );
        });
    }
});
