<div class="cartmini__wrapper d-flex justify-content-between flex-column">
    <div class="cartmini__top-wrapper">
        <div class="cartmini__top p-relative">
            <div class="cartmini__top-title">
                <h4><?php echo e(__('Shopping cart')); ?></h4>
            </div>
            <div class="cartmini__close" title="Close">
                <button type="button" class="cartmini__close-btn cartmini-close-btn" title="Close">
                    <?php if (isset($component)) { $__componentOriginal73995948b3bd877b76251b40caf28170 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal73995948b3bd877b76251b40caf28170 = $attributes; } ?>
<?php $component = Botble\Icon\View\Components\Icon::resolve(['name' => 'ti ti-x'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Botble\Icon\View\Components\Icon::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal73995948b3bd877b76251b40caf28170)): ?>
<?php $attributes = $__attributesOriginal73995948b3bd877b76251b40caf28170; ?>
<?php unset($__attributesOriginal73995948b3bd877b76251b40caf28170); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal73995948b3bd877b76251b40caf28170)): ?>
<?php $component = $__componentOriginal73995948b3bd877b76251b40caf28170; ?>
<?php unset($__componentOriginal73995948b3bd877b76251b40caf28170); ?>
<?php endif; ?>
                </button>
            </div>
        </div>

        <?php if($ajax ?? false): ?>
            <?php echo Theme::partial('mini-cart.content'); ?>

        <?php else: ?>
            <div data-bb-toggle="mini-cart-content-slot"></div>
        <?php endif; ?>
    </div>

    <?php if($ajax ?? false): ?>
        <?php echo Theme::partial('mini-cart.footer'); ?>

    <?php else: ?>
        <div data-bb-toggle="mini-cart-footer-slot"></div>
    <?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/partials/mini-cart.blade.php ENDPATH**/ ?>