<?php $__currentLoopData = RvMedia::getConfig('libraries.javascript', []); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $js): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <script
        src="<?php echo e(asset($js)); ?>?v=<?php echo e(get_cms_version()); ?>"
        type="text/javascript"
    ></script>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform/core/media/resources/views/footer.blade.php ENDPATH**/ ?>