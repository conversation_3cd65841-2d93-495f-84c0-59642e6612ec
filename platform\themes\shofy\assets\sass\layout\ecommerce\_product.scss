@use '../../utils' as *;

/*----------------------------------------*/
/*  8.9 Product Card CSS START
/*----------------------------------------*/

.#{$theme-prefix}-product {
    $self: &;
    &-item {
        background-color: var(--tp-common-white);
        border: 1px solid var(--tp-border-primary);
        border-radius: 8px;

        &:hover {
            box-shadow: 0px 4px 10px rgba(1, 15, 28, 0.14);
            border-color: var(--tp-common-white);
            #{$self} {
                &-thumb {
                    & img {
                        @include transform(scale(1.1));
                    }
                }
                &-action {
                    right: 20px;
                    visibility: visible;
                    opacity: 1;
                }
                &-add-cart-btn-large-wrapper {
                    bottom: 0;
                    visibility: visible;
                    opacity: 1;
                }
            }
        }
    }
    &-badge {
        position: absolute;
        top: 16px;
        right: 20px;
        z-index: 1;
        & span {
            font-family: var(--primary-font);
            font-weight: 500;
            font-size: 14px;
            line-height: 1;
            color: var(--tp-common-white);
            background-color: var(--tp-theme-primary);
            border-radius: 4px;
            display: inline-block;
            padding: 4px 8px 2px;

            &.product-hot {
                background-color: var(--tp-pink-2);
            }
            &.product-trending {
                background-color: var(--tp-green-1);
            }
            &.product-offer {
                background-color: var(--tp-orange-1);
            }
            &.product-sale {
                background-color: var(--tp-blue-1);
            }
            &.product-out-stock {
                background-color: #000;
            }
        }
    }
    &-thumb {
        border-bottom: 1px solid var(--tp-border-primary);
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        & img {
            @extend %tp-transition;

            @media #{$sm, $xs} {
                width: 100%;
            }
        }
    }
    &-content {
        padding: 10px 30px 20px;
    }
    &-category {
        & > a {
            font-family: var(--primary-font);
            font-weight: 500;
            font-size: 12px;

            &:hover {
                color: var(--tp-theme-primary);
            }
        }
    }
    &-title {
        font-family: var(--primary-font);
        font-weight: 500;
        font-size: 15px;
        line-height: 1.33;
        margin-bottom: 7px;

        & a {
            &:hover {
                color: var(--tp-theme-primary);
            }
        }
    }
    &-rating {
        &-icon {
            margin-right: 6px;
            display: flex;
            align-items: center;
            & span {
                color: var(--tp-yellow-3);
                font-size: 12px;
                &:not(:last-child) {
                    margin-right: 2px;
                }
            }
        }
        &-text {
            & a {
                font-family: var(--primary-font);
                font-weight: 500;
                font-size: 12px;
            }
        }
    }
    &-price {
        font-family: var(--primary-font);
        font-weight: 700;
        font-size: 15px;
        letter-spacing: -0.02em;
        color: var(--tp-theme-primary);

        &.old-price {
            color: #55585b;
            font-size: 12px;
            text-decoration: line-through;
            font-weight: 500;
            margin-left: 6px;
        }
    }
    &-tab {
        & .nav-tabs {
            background-color: var(--tp-common-white);
            padding-left: 50px;

            @media #{$sm, $xs} {
                padding-left: 0;
            }
            & .nav-item {
                &:not(:last-child) {
                    margin-right: 47px;

                    @media #{$xs} {
                        margin-right: 30px;
                    }
                }
                & .nav-link {
                    font-family: var(--primary-font);
                    font-weight: 400;
                    font-size: 20px;
                    color: var(--tp-text-3);
                    position: relative;
                    &.active {
                        color: var(--tp-common-black);
                        #{$self} {
                            &-tab-line {
                                opacity: 1;
                                visibility: visible;
                            }
                        }
                    }
                }
            }
        }
        &-line {
            position: absolute;
            bottom: -13px;
            left: 0;
            visibility: hidden;
            opacity: 0;
            @extend %tp-transition;
            & svg {
                & path {
                    stroke: var(--tp-theme-primary);
                }
            }
        }
        &-border {
            position: relative;
            &::after {
                position: absolute;
                content: '';
                left: -80px;
                top: 50%;
                width: 420px;
                height: 2px;
                background-color: var(--tp-border-secondary);
                z-index: -1;
                @media #{$sm, $xs} {
                    display: none;
                }
            }
        }
    }
    &-action {
        position: absolute;
        right: -20px;
        bottom: 25px;
        z-index: 1;
        visibility: hidden;
        opacity: 0;
        @extend %tp-transition;

        &-item {
            box-shadow: 0px 1px 3px rgba(1, 15, 28, 0.14);
            border-radius: 4px;
        }
        &-btn {
            --tp-btn-color: var(--tp-common-black);

            position: relative;
            display: inline-block;
            width: 40px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            font-size: 18px;
            color: var(--tp-btn-color);
            background-color: var(--tp-common-white);
            border: 1px solid var(--tp-border-primary);
            border-bottom: 0;

            &:first-child {
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            &:last-child {
                border-bottom-left-radius: 4px;
                border-bottom-right-radius: 4px;
                border-bottom: 1px solid var(--tp-border-primary);
            }

            & svg {
                @extend %tp-svg-y-2;
            }

            &:hover,
            &.active {
                --tp-btn-color: var(--tp-common-white);
                background-color: var(--tp-theme-primary);
                border-color: var(--tp-theme-primary);
            }

            &:hover {
                #{$self} {
                    &-tooltip {
                        visibility: visible;
                        opacity: 1;
                        @include transform(translateX(-8px) translateY(-50%));
                    }
                }
            }
        }
    }
    &-offer {
        &-slider {
            margin: 0 -15px;
            @media #{$xs} {
                margin: 0;
            }
            &-active {
                padding: 15px;

                @media #{$xs} {
                    padding-left: 0;
                    padding-right: 0;
                }
            }
        }
        &-item {
            box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.1);
            &:hover {
                box-shadow: 0px 12px 14px rgba(1, 15, 28, 0.06);
            }

            #{$self} {
                &-content {
                    padding: 20px 30px 30px;

                    @media #{$xs} {
                        padding: 20px 30px 30px;
                    }
                }
                &-price-wrapper {
                    margin-bottom: 20px;
                }
                &-action {
                    bottom: 70px;
                }
            }
        }
        &-more {
            position: relative;
            padding-left: 50px;
            @media #{$xs} {
                padding-left: 0;
            }
            &-border {
                position: absolute;
                right: 210px;
                top: 50%;
                width: 650px;
                height: 2px;
                background-color: var(--tp-common-white);
                z-index: -1;
                @media #{$xl, $lg, $md, $sm, $xs} {
                    right: 0;
                }
                @media #{$md} {
                    width: 400px;
                }
                @media #{$sm, $xs} {
                    display: none;
                }
            }
        }
    }
    &-countdown {
        &-inner {
            & ul {
                & li {
                    display: inline-block;
                    list-style: none;
                    font-family: var(--primary-font);
                    font-weight: 500;
                    font-size: 10px;
                    line-height: 1;
                    text-align: center;
                    text-transform: uppercase;
                    background-color: var(--tp-common-white);
                    border: 1px solid var(--tp-border-3);
                    border-radius: 5px;
                    width: 50px;
                    height: 50px;
                    padding-top: 8px;

                    &:not(:last-child) {
                        margin-right: 3px;
                    }
                    & span {
                        display: block;
                        font-family: var(--primary-font);
                        font-weight: 700;
                        font-size: 20px;
                        line-height: 1;
                        text-align: center;
                        letter-spacing: -0.04em;
                        text-transform: uppercase;
                        color: var(--tp-common-black);
                    }
                }
            }
        }
    }
    &-add-cart-btn-large {
        &-wrapper {
            position: absolute;
            bottom: -40px;
            left: 0;
            right: 0;
            width: 100%;
            visibility: hidden;
            opacity: 0;
            @extend %tp-transition;
        }

        color: var(--tp-common-white);
        background-color: var(--tp-common-black);
        font-size: 14px;
        font-weight: 700;
        font-family: var(--primary-font);
        display: inline-block;
        width: 100%;
        padding: 8px 30px;

        & svg,
        & i {
            margin-right: 5px;
        }

        & svg {
            @extend %tp-svg-y-2;
        }

        &:hover {
            color: var(--tp-common-white);
            background-color: var(--tp-theme-primary);
        }
    }
    &-sm {
        &-item {
            &:not(:last-child) {
                padding-bottom: 12px;
                margin-bottom: 12px;
                border-bottom: 1px solid var(--tp-border-primary);
            }
            #{$self} {
                &-thumb {
                    border-radius: 5px;

                    @media #{$xs} {
                        max-width: 100px;
                    }
                }
                &-category {
                    line-height: 1;
                    margin-bottom: 3px;
                }
                &-title {
                    margin-bottom: 13px;
                }
                &-rating {
                    line-height: 1;
                    margin-bottom: 3px;
                    @media #{$xs} {
                        margin-bottom: 7px;
                    }
                    &-icon {
                        @media #{$xs} {
                            margin-bottom: 5px;
                        }
                    }
                }
            }
            &:hover {
                & #{$self} {
                    &-thumb {
                        & img {
                            @include transform(scale(1.1));
                        }
                    }
                }
            }
        }
    }
    &-list {
        &-item {
            margin-bottom: 24px;

            &:hover {
                #{$self} {
                    &-list-thumb {
                        & img {
                            @include transform(scale(1.1));
                        }
                    }

                    &-action-2 {
                        left: 24px;
                        visibility: visible;
                        opacity: 1;
                    }
                }
            }

            #{$self} {
                &-action-2 {
                    top: 50%;
                    @include transform(translateY(-50%));
                }
            }
            & .tp-product-list-content {
                border: 1px solid #e0e2e3;
                border-left: 0;
                padding-left: 60px;
                padding-right: 70px;

                @media #{$lg} {
                    padding-left: 30px;
                    padding-right: 30px;
                }

                @media #{$md} {
                    padding-left: 35px;
                    padding-right: 35px;
                }
                @media #{$sm, $xs} {
                    border: 1px solid #e0e2e3;
                    border-top: 0;
                }
                @media #{$xs} {
                    padding-left: 25px;
                    padding-right: 25px;
                }

                & .tp-product-content-2 {
                    padding-top: 33px;
                    padding-bottom: 33px;
                }

                & p {
                    line-height: 1.5;
                    color: #55585b;
                    margin-bottom: 25px;
                }

                & .tp-product-price-wrapper-2 {
                    margin-bottom: 12px;
                }
            }
        }
        &-thumb {
            flex: 0 0 auto;
            border: 1px solid #e0e2e3;

            @media (min-width: 991px) {
                width: 300px;
                border-inline-end: 0;
            }

            & a {
                display: block;
                width: 100%;
                height: 100%;

                & img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    @extend %tp-transition;
                }
            }
        }
    }
    &-related {
        &-slider {
            & .#{$theme-prefix}-swiper-scrollbar {
                background-color: #edeff2;
                width: calc(100% - 410px);
                margin: auto;
                @media #{$lg} {
                    width: calc(100% - 300px);
                }
                @media #{$md} {
                    width: calc(100% - 200px);
                }
                @media #{$sm} {
                    width: calc(100% - 100px);
                }
                @media #{$xs} {
                    width: 100%;
                }
            }
        }
    }
}
.#{$theme-prefix}-product-gadget {
    &-sidebar {
        position: sticky;
        top: 113px;
    }
    &-thumb {
        position: absolute;
        bottom: 0;
        right: -18px;

        @media #{$xs} {
            right: -60px;
        }
    }
    &-categories {
        border: 3px solid var(--tp-pink-4);
        border-radius: 8px;
        padding: 37px 40px;
    }
    &-categories-title {
        font-family: var(--primary-font);
        font-weight: 500;
        font-size: 20px;
        line-height: 1.2;
        position: relative;
        padding-bottom: 8px;
        margin-bottom: 22px;
        &::after {
            position: absolute;
            content: '';
            left: 0;
            width: 150px;
            height: 1px;
            background-color: var(--tp-theme-primary);
            bottom: 0;
        }
    }

    &-categories-list {
        margin-bottom: 27px;
        & ul {
            & li {
                list-style: none;
                line-height: 1;
                margin-bottom: 10px;
                & a {
                    font-family: var(--primary-font);
                    font-weight: 500;
                    font-size: 14px;
                    position: relative;
                    padding-left: 13px;
                    &::after {
                        position: absolute;
                        content: '';
                        left: 0;
                        top: 7px;
                        height: 3px;
                        width: 3px;
                        background-color: var(--tp-text-2);
                        border-radius: 50%;
                    }
                    &:hover {
                        color: var(--tp-theme-primary);
                    }
                }
            }
        }
    }
    &-btn {
        & .tp-link-btn {
            font-family: var(--primary-font);
        }
    }
    &-banner {
        $self-banner: &;
        &-slider {
            &-active {
                & .swiper-slide-active {
                    & #{$self-banner} {
                        &-title,
                        &-subtitle,
                        &-content p,
                        &-price,
                        &-btn a {
                            @include animation-name();
                        }
                        &-thumb {
                            & img {
                                @include animation-name(fadeInRight);
                            }
                            &-gradient {
                                @include animation-name(fadeInRight);
                            }
                            &-shape {
                                .tp-offer-shape {
                                    @include animation-name(fadeInRight);
                                }
                            }
                        }
                    }
                }
            }
            &-dot {
                &.#{$theme-prefix}-swiper-dot {
                    position: absolute;
                    right: 20px;
                    left: auto;
                    bottom: 50%;
                    @include transform(translateY(50%));
                    z-index: 1;
                    width: auto;
                    display: flex;
                    flex-direction: column;

                    &.#{$theme-prefix}-swiper-dot {
                        .swiper-pagination-bullet {
                            margin: 3px 0;
                            background-color: rgba($color: #fff, $alpha: 0.2);
                            &.swiper-pagination-bullet-active {
                                background-color: var(--tp-common-white);
                            }
                        }
                    }
                }
            }
        }
        &-item {
            border-radius: 8px;
        }
        &-content {
            padding: 45px 30px 41px;
        }
        &-price {
            display: inline-block;
            font-family: var(--primary-font);
            font-weight: 500;
            font-size: 14px;
            color: var(--tp-common-white);
            @include animation-control(0.1s);
        }
        &-title {
            font-family: var(--primary-font);
            font-weight: 700;
            font-size: 24px;
            color: var(--tp-common-white);
            @include animation-control(0.2s);
        }
    }
}

/* =================
product all slider
==================*/

/* arrival slider home electronics*/
.#{$theme-prefix} {
    &-product-arrival {
        &-slider {
            margin: 0 -15px;
            @media #{$xs} {
                margin: 0;
            }
        }
        &-active {
            padding: 15px;
            @media #{$xs} {
                padding-left: 0;
                padding-right: 0;
            }
        }
        &-border {
            position: relative;
            padding-left: 50px;
            display: flex;
            gap: 4px;
            background-color: var(--tp-common-white);
            &::after {
                position: absolute;
                content: '';
                right: 0;
                top: 50%;
                width: 930px;
                height: 2px;
                background-color: var(--tp-border-secondary);
                z-index: -1;

                @media #{$xl, $lg} {
                    width: 700px;
                }

                @media #{$md} {
                    width: 430px;
                }
                @media #{$sm, $xs} {
                    display: none;
                }
            }
        }
    }
}
