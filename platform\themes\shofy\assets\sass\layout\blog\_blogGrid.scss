@use '../../utils' as *;
/*----------------------------------------*/
/*  5.5 Blog Grid css
/*----------------------------------------*/

.#{$theme-prefix}-blog-grid {
    $self: &;

    &-wrapper {
        margin-right: 64px;

        @media #{$lg, $md, $sm, $xs} {
            margin-right: 0;
        }
    }

    &-item {
        border: 1px solid #e0e2e3;

        &:hover {
            #{$self} {
                &-thumb {
                    & img {
                        @include transform(scale(1.1));
                    }
                }
            }
        }
    }

    &-meta {
        margin-bottom: 11px;

        & > span {
            font-size: 15px;
            position: relative;
            display: inline-block;

            &:not(:last-child) {
                padding-right: 12px;

                &::after {
                    position: absolute;
                    content: '';
                    right: 0;
                    top: 0;
                    background-color: #e6e7e8;
                    width: 1px;
                    height: 20px;
                }
            }

            & a {
                &:hover {
                    color: var(--tp-theme-primary);
                }
            }
        }

        & span span {
            margin-right: 1px;

            & svg {
                @extend %tp-svg-y-2;
            }
        }
    }

    &-title {
        font-size: 26px;
        font-weight: 500;
        margin-bottom: 13px;

        @media #{$xs} {
            font-size: 24px;
        }

        & a {
            &:hover {
                color: var(--tp-theme-primary);
            }
        }
    }

    &-thumb {
        & img {
            @extend %tp-transition;
        }
    }

    &-content {
        padding: 0 30px 32px;

        @media #{$lg, $xs} {
            padding: 0 15px 32px;
        }

        & p {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 20px;
        }
    }

    &-top {
        border: 1px solid rgba($color: $black, $alpha: 0.1);
        padding: 6px 20px 6px 25px;
    }

    &-result {
        & p {
            color: #818487;
            margin-bottom: 0;
        }
    }

    &-tab {
        & .nav-tabs {
            & .nav-link {
                color: #818487;
                font-size: 20px;
                position: relative;

                &#nav-grid-tab {
                    @include transform(translateY(-1px));
                }

                & svg {
                    @extend %tp-svg-y-2;
                }

                &:not(:last-child) {
                    margin-right: 13px;
                    padding-right: 13px;

                    &::after {
                        position: absolute;
                        content: '';
                        right: 0;
                        top: 5px;
                        width: 1px;
                        height: 20px;
                        background-color: rgba($color: $black, $alpha: 0.1);
                    }
                }

                &.active {
                    color: var(--tp-common-black);
                }
            }
        }
    }

    &-style2 {
        border: 0;
        background-color: var(--tp-common-white);
        box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.1);
    }
}

.#{$theme-prefix}-blog-list {
    $self: &;
    $blog: '.tp-blog-grid';

    &-item {
        border: 1px solid #e0e2e3;
        margin-bottom: 30px;

        &:not(:last-child) {
            margin-bottom: 20px;
        }

        &:hover {
            & #{$self} {
                &-thumb {
                    & img {
                        @include transform(scale(1.1));
                    }
                }
            }
        }
    }

    &-thumb {
        width: 383px;
        flex: 0 0 auto;
        overflow: hidden;

        @media #{$lg, $sm, $xs} {
            width: 100%;
        }
        @media #{$md} {
            width: 340px;
        }

        & img {
            max-width: 100%;
            height: 100%;
            @extend %tp-transition;
            @media #{$lg, $sm, $xs} {
                width: 100%;
            }
        }
    }

    &-content {
        & #{$blog} {
            &-title {
                font-size: 30px;
                font-weight: 500;
                margin-bottom: 12px;

                @media #{$xs} {
                    font-size: 24px;
                }
            }

            &-meta {
                margin-bottom: 5px;
            }

            &-content {
                padding: 40px 40px 45px 40px;

                @media #{$md} {
                    padding: 40px 30px 45px 30px;
                }
                @media #{$xs} {
                    padding: 25px 20px 30px 20px;
                }

                & p {
                    margin-bottom: 23px;
                }
            }
        }
    }
}
