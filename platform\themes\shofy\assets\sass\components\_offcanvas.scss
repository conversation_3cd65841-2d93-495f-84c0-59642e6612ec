@use '../utils' as *;

/*----------------------------------------*/
/*  2.10 Offcanvas
/*----------------------------------------*/

.offcanvas {
    $self: &;
    &__area {
        position: fixed;
        inset-inline-end: 0;
        top: 0;
        width: 380px;
        height: 100%;
        @include transform(translateX(calc(100% + 80px)));
        background: var(--tp-common-white) none repeat scroll 0 0;
        @include tp-transition-mul((all 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86)));
        z-index: 99999;

        overflow-y: scroll;
        overscroll-behavior-y: contain;
        scrollbar-width: none;

        &::-webkit-scrollbar {
            display: none; /* for Chrome, Safari, and Opera */
        }
        &.offcanvas-opened {
            @include transform(translateX(0));
            opacity: 1;
        }

        @media (max-width: 380px) {
            width: 100%;
        }
    }
    &__top {
        @media #{$md, $sm, $xs} {
            margin-bottom: 30px;
        }
    }
    &__content {
        padding-bottom: 120px;
    }
    &__wrapper {
        position: relative;
        padding: 25px 20px 145px;
        z-index: 1;
        min-height: 100%;

        @media #{$xs} {
            padding: 20px;
        }
    }
    &__close {
        position: absolute;
        top: 25px;
        right: 20px;

        @media #{$xs} {
            right: 20px;
            top: 20px;
        }
        &-btn {
            display: inline-block;
            font-size: 16px;
            height: 30px;
            width: 30px;
            line-height: 30px;
            background-color: transparent;
            color: var(--tp-common-black);
            background-color: #ececec;
            &:hover {
                background-color: var(--tp-theme-primary);
                border-color: transparent;
                color: var(--tp-common-white);
            }

            & svg {
                @include transform(translateY(-1px));
            }
        }
    }
    &__inner {
        & h4 {
            font-family: var(--tp-ff-space);
            font-size: 24px;
            color: var(--tp-common-white);
            line-height: 1.3;
            margin-bottom: 15px;
        }
        & p {
            font-family: var(--tp-ff-inter);
            font-size: 16px;
            line-height: 22px;
            color: var(--tp-common-white);
            margin-bottom: 30px;
        }
        & > img {
            margin-bottom: 30px;
        }
    }
    &__menu {
        & ul {
            & li {
                list-style: none;
                margin-bottom: 10px;
                &:last-child {
                    margin-bottom: 0;
                }
                & a {
                    font-weight: 700;
                    font-size: 20px;
                    color: var(--tp-common-black);
                }

                &:hover {
                    & > a {
                        color: var(--tp-theme-primary);
                    }
                }
            }
        }
    }
    &__text {
        & p {
            font-family: var(--tp-ff-inter);
            font-weight: 400;
            font-size: 14px;
            line-height: 1.7;
            letter-spacing: 0.01em;
            color: var(--tp-common-black-4);
            margin-bottom: 25px;
        }
    }
    &__contact {
        &-call {
            font-family: var(--tp-ff-space);
            font-weight: 700;
            font-size: 20px;
            color: var(--tp-common-black);
            margin-bottom: 5px;

            & a {
                &:hover {
                    color: var(--tp-theme-primary);
                }
            }
        }
        &-mail {
            font-family: var(--tp-ff-space);
            font-weight: 400;
            font-size: 16px;
            color: var(--tp-text-1);

            & a {
                &:hover {
                    color: var(--tp-theme-primary);
                }
            }
        }
    }
    &__social {
        margin-bottom: 22px;
        padding-bottom: 40px;
        border-bottom: 1px solid var(--tp-border-primary);
        & a {
            display: inline-block;
            width: 40px;
            height: 40px;
            line-height: 38px;
            text-align: center;
            background: transparent;
            color: var(--tp-common-black);
            margin-right: 5px;
            border: 2px solid var(--tp-border-primary);

            &:hover {
                background: var(--tp-theme-primary);
                color: var(--tp-common-white);
                border-color: var(--tp-theme-primary);
            }
        }
    }
    &__info {
        &-item {
            margin-bottom: 38px;
            &-title {
                font-family: var(--tp-ff-space);
                font-weight: 700;
                font-size: 18px;
                color: var(--tp-common-white);
                margin-bottom: 12px;
            }

            & p {
                font-size: 16px;
                color: var(--tp-text-16);
                margin-bottom: 0;
                line-height: 22px;

                & a {
                    &:hover {
                        color: var(--tp-theme-primary);
                    }
                }
            }
        }
    }
    &__categoryssss {
        & ul {
            padding-top: 15px;
            & li {
                list-style: none;
                position: relative;
                width: 100%;
                padding-left: 20px;
                padding-right: 20px;
                & a {
                    display: block;
                    font-size: 16px;
                    color: var(--tp-common-black);
                    position: relative;

                    & > i {
                        display: inline-block;
                        width: 11%;
                        margin-right: 13px;
                        @include transform(translateY(4px));
                        font-size: 21px;
                        line-height: 1;
                    }
                    & .menu-text {
                        font-size: 16px;
                        line-height: 11px;
                        border-bottom: 1px solid #eaebed;
                        width: 82%;
                        display: inline-block;
                        padding: 19px 0 17px;
                    }
                }

                &:hover {
                    & > a {
                        color: var(--tp-theme-primary);
                        &::after {
                            color: var(--tp-theme-primary);
                        }
                    }

                    & .mega-menu {
                        visibility: visible;
                        opacity: 1;
                        top: 0;
                    }
                }

                &.has-dropdown {
                    & > a {
                        & .dropdown-toggle-btn {
                            position: absolute;
                            right: 0;
                            top: 50%;
                            @extend %translateY1_2;
                            font-size: 16px;
                            color: #7f8387;
                            @extend %tp-transition;

                            &.dropdown-opened {
                                @include transform(translateY(-50%) rotate(90deg));
                            }
                        }

                        &.expanded {
                            color: var(--tp-theme-primary);

                            & .dropdown-toggle-btn.dropdown-opened,
                            & .dropdown-toggle-btn.dropdown-opened i {
                                color: var(--tp-theme-primary);
                            }
                        }
                    }
                    &:hover {
                        & > a {
                            &::after {
                                color: var(--tp-theme-green);
                            }
                        }
                    }
                }

                &:last-child {
                    & a {
                        & span {
                            border-bottom: 0;
                        }
                    }
                }

                & .mega-menu {
                    position: absolute;
                    top: 40px;
                    left: 100%;
                    background-color: var(--tp-common-white);
                    z-index: 1;
                    min-width: 670px;
                    @include flexbox();
                    box-shadow: 14px 20px 40px rgba(1, 15, 28, 0.14);
                    border-radius: 0px 8px 8px 0px;
                    visibility: hidden;
                    opacity: 0;
                    @extend %tp-transition;
                    /* left right */

                    & *.menu-text {
                        display: none;
                    }

                    & .mega-menu-left {
                        width: 60%;
                    }
                    & .mega-menu-right {
                        width: 40%;
                        border: 4px solid var(--tp-common-white);
                        border-radius: 0 6px 6px 0;
                        padding-left: 34px;
                        padding-right: 33px;
                        padding-top: 30px;
                        &-title {
                            font-weight: 500;
                            font-size: 20px;
                            margin-bottom: 7px;
                        }
                    }

                    & .mega-menu-img {
                        height: 120px;
                        @include flexbox();
                        justify-content: center;
                        align-items: flex-end;
                        margin-bottom: 40px;
                    }

                    & .mega-menu-list {
                        @include flexbox();
                        border-bottom: 1px solid #eaebed;
                        & ul {
                            padding-left: 34px;
                            padding-bottom: 30px;
                            &:not(:last-child) {
                                border-right: 1px solid #eaebed;
                            }
                            & li {
                                padding: 0;
                                &:not(:last-child) {
                                    margin-bottom: 4px;
                                }
                                & a {
                                    font-weight: 400;
                                    font-size: 15px;
                                    color: #55585b;

                                    &.mega-menu-title {
                                        font-weight: 500;
                                        font-size: 20px;
                                        color: var(--tp-common-black);
                                        margin-bottom: 10px;
                                    }

                                    &:hover {
                                        color: var(--tp-theme-green);
                                    }
                                }

                                & ul {
                                    padding: 0;
                                    &:not(:last-child) {
                                        border: 0;
                                    }
                                }
                            }
                        }
                    }

                    & .mega-menu-brand {
                        display: flex;
                        justify-content: center;
                        & a {
                            margin: 0 27px;
                            display: inline-block;
                            border: 0;
                            padding: 26px 0 20px;
                            & img {
                                opacity: 0.5;
                                @extend %tp-transition;
                            }

                            & .menu-text {
                                display: none;
                            }

                            &:hover {
                                & img {
                                    opacity: 1;
                                }
                            }
                        }
                    }

                    & .menu-shop {
                        &-thumb {
                            & a {
                                border: 0;
                            }
                            & img {
                                width: 70px;
                                height: auto;
                                object-fit: cover;
                                margin-right: 17px;
                            }
                        }
                        &-item {
                            padding-top: 14px;
                            &:not(:last-child) {
                                padding-bottom: 15px;
                                border-bottom: 1px solid #eaebed;
                            }
                        }
                        &-meta {
                            & span {
                                font-size: 14px;
                                line-height: 1;
                                display: inline-block;
                                margin-bottom: 1px;

                                & a {
                                    color: #55585b;
                                    font-size: 14px;
                                    &:hover {
                                        color: var(--tp-theme-green);
                                    }
                                }
                            }
                        }
                        &-title {
                            font-weight: 400;
                            font-size: 16px;
                            line-height: 0.7;

                            & a {
                                &:hover {
                                    color: var(--tp-theme-green);
                                }
                            }
                        }
                        &-price {
                            font-weight: 500;
                            font-size: 15px;
                            line-height: 1;
                            letter-spacing: -0.02em;
                            color: var(--tp-common-black);

                            &.new-price {
                                color: var(--tp-common-black);
                            }
                            &.old-price {
                                font-weight: 400;
                                font-size: 13px;
                                text-decoration-line: line-through;
                                color: var(--tp-text-1);
                            }
                            &-wrapper {
                            }
                        }
                    }
                }
            }
        }
        & nav {
            display: none;
        }
    }
    &__lang,
    &__currency {
        position: relative;
        padding-right: 0;
        $self: &;
        z-index: 9;

        & .nice-select {
            padding: 0;
            padding-right: 18px;
            padding-left: 28px;
            font-size: 14px;
            border: 0;
            color: var(--tp-text-2);
            background-color: transparent;

            & .current {
                color: var(--tp-text-2);
                font-size: 14px;
                font-weight: 500;
            }

            & .list {
                border-radius: 0;
                margin-top: 0;
                left: auto;
                right: 0;

                & .option {
                    color: var(--tp-text-2);
                    &:hover,
                    &.selected.focus {
                        color: var(--tp-theme-1);
                    }
                }
            }
        }

        &-selected-lang,
        &-selected-currency {
            color: var(--tp-text-2);
            font-size: 14px;
            font-weight: 500;
            background-color: transparent;
            position: relative;
            padding-right: 20px;
            @extend %tp-transition;

            &:hover {
                cursor: pointer;
            }
            &::after {
                position: absolute;
                content: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M6 9l6 6l6 -6" /></svg>');
                right: 0;
                top: calc(50% + 3px);
                @include transform(translateY(-50%));
                color: var(--tp-text-2);
                @extend %tp-transition;
            }
        }
        &-wrapper {
            position: relative;
        }
        &-list {
            position: absolute;
            bottom: 150%;
            right: 0;
            background-color: var(--tp-grey-1);
            z-index: 11;
            padding: 15px 28px;
            border-radius: 4px;
            visibility: hidden;
            opacity: 0;
            @extend %tp-transition;

            &.tp-lang-list {
                &.tp-lang-list-open {
                    visibility: visible;
                    opacity: 1;
                    bottom: 112%;
                }
            }
            &.tp-currency-list {
                &.tp-currency-list-open {
                    visibility: visible;
                    opacity: 1;
                    bottom: 112%;
                }
            }

            & li {
                list-style: none;
                color: var(--tp-common-black);
                text-align: left;

                &:hover {
                    color: var(--tp-theme-primary);
                    cursor: pointer;
                }
            }
        }
    }
    &__bottom {
        position: absolute;
        bottom: 24px;
        left: 40px;
        right: 40px;
        z-index: 1;
        border-top: 1px solid var(--tp-border-primary);
        padding-top: 15px;
    }
    &__contact {
        padding-left: 20px;
        padding-right: 20px;
        &-icon {
        }
        &-title {
            font-size: 20px;
            font-weight: 500;
            font-family: var(--primary-font);
            margin-bottom: 0;
            & a {
                &:hover {
                    color: var(--tp-theme-primary);
                }
            }
        }
    }
    &__btn {
        padding-left: 20px;
        padding-right: 20px;

        & .tp-btn-2 {
            padding: 11px 30px;
            font-size: 14px;
        }

        & .tp-btn-2 {
            border-radius: 0;
        }
    }
    &__style-darkRed {
        .tp-offcanvas-category-toggle {
            background-color: var(--secondary-color);
        }

        .offcanvas__close-btn:hover {
            background-color: var(--secondary-color);
        }

        .offcanvas__category ul li:hover > a {
            color: var(--secondary-color);
        }

        .mean-container .mean-nav ul li.has-dropdown:hover > a,
        .mean-container .mean-nav ul li.has-dropdown:hover .mean-expand {
            color: var(--secondary-color);
        }

        .mean-container .mean-nav ul li.dropdown-opened > a,
        .mean-container .mean-nav ul li a:hover,
        .mean-container .mean-nav ul li.dropdown-opened > a.mean-expand.mean-clicked,
        .mean-container .mean-nav ul li a.mean-expand:hover i,
        .mean-container .mean-nav ul li a.mean-expand.mean-clicked:hover i,
        .mean-container .mean-nav ul li.dropdown-opened > a.mean-expand.mean-clicked i,
        .offcanvas__lang-list li:hover,
        .offcanvas__currency-list li:hover {
            color: var(--secondary-color);
        }

        // menu css start

        & .offcanvas__btn .tp-btn-2:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: var(--tp-common-white);
        }

        .tp-main-menu-mobile ul li:hover > a {
            color: var(--secondary-color);
        }

        .tp-main-menu-mobile ul li:hover > a .dropdown-toggle-btn i {
            color: var(--secondary-color);
        }
        .tp-main-menu-mobile ul li.has-dropdown > a.expanded {
            color: var(--secondary-color);
        }
        .tp-main-menu-mobile ul li.has-dropdown > a.expanded .dropdown-toggle-btn.dropdown-opened {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: var(--tp-common-white);
        }

        .tp-main-menu-mobile ul li.has-dropdown > a .dropdown-toggle-btn:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: var(--tp-common-white);
        }

        .tp-menu-showcase-btn {
            border-radius: 0;
            background-color: var(--secondary-color);
        }

        .home-menu-title a:hover {
            color: var(--secondary-color);
        }

        // category css start

        .tp-category-mobile-menu ul li.has-dropdown > a .dropdown-toggle-btn:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: var(--tp-common-white);
        }
        .tp-category-mobile-menu ul li:hover > a .dropdown-toggle-btn i {
            color: var(--secondary-color);
        }
        .tp-category-mobile-menu ul li.has-dropdown > a.expanded {
            color: var(--secondary-color);
        }
        .tp-category-mobile-menu ul li.has-dropdown > a.expanded .dropdown-toggle-btn.dropdown-opened {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: var(--tp-common-white);
        }
    }
    &__radius {
        & .tp-btn-2 {
            border-radius: 6px;
        }
    }
    &__style-brown {
        .tp-offcanvas-category-toggle {
            background-color: var(--tp-theme-brown);
        }

        .offcanvas__category ul li:hover > a {
            color: var(--tp-theme-brown);
        }

        .offcanvas__close-btn:hover {
            background-color: var(--tp-theme-brown);
        }

        .mean-container .mean-nav ul li.has-dropdown:hover > a,
        .mean-container .mean-nav ul li.has-dropdown:hover .mean-expand {
            color: var(--tp-theme-brown);
        }

        .mean-container .mean-nav ul li.dropdown-opened > a,
        .mean-container .mean-nav ul li a:hover,
        .mean-container .mean-nav ul li.dropdown-opened > a.mean-expand.mean-clicked,
        .mean-container .mean-nav ul li a.mean-expand:hover i,
        .mean-container .mean-nav ul li a.mean-expand.mean-clicked:hover i,
        .mean-container .mean-nav ul li.dropdown-opened > a.mean-expand.mean-clicked i,
        .offcanvas__lang-list li:hover,
        .offcanvas__currency-list li:hover {
            color: var(--tp-theme-brown);
        }

        & .offcanvas__btn .tp-btn-2:hover {
            background-color: var(--tp-theme-brown);
            border-color: var(--tp-theme-brown);
            color: var(--tp-common-white);
        }

        .tp-main-menu-mobile ul li:hover > a {
            color: var(--tp-theme-brown);
        }

        .tp-main-menu-mobile ul li:hover > a .dropdown-toggle-btn i {
            color: var(--tp-theme-brown);
        }
        .tp-main-menu-mobile ul li.has-dropdown > a.expanded {
            color: var(--tp-theme-brown);
        }
        .tp-main-menu-mobile ul li.has-dropdown > a.expanded .dropdown-toggle-btn.dropdown-opened {
            background-color: var(--tp-theme-brown);
            border-color: var(--tp-theme-brown);
            color: var(--tp-common-white);
        }

        .tp-main-menu-mobile ul li.has-dropdown > a .dropdown-toggle-btn:hover {
            background-color: var(--tp-theme-brown);
            border-color: var(--tp-theme-brown);
            color: var(--tp-common-white);
        }

        .tp-menu-showcase-btn {
            border-radius: 0;
            background-color: var(--tp-theme-brown);
        }

        .home-menu-title a:hover {
            color: var(--tp-theme-brown);
        }

        // category css start

        .tp-category-mobile-menu ul li.has-dropdown > a .dropdown-toggle-btn:hover {
            background-color: var(--tp-theme-brown);
            border-color: var(--tp-theme-brown);
            color: var(--tp-common-white);
        }
        .tp-category-mobile-menu ul li:hover > a .dropdown-toggle-btn i {
            color: var(--tp-theme-brown);
        }
        .tp-category-mobile-menu ul li.has-dropdown > a.expanded {
            color: var(--tp-theme-brown);
        }
        .tp-category-mobile-menu ul li.has-dropdown > a.expanded .dropdown-toggle-btn.dropdown-opened {
            background-color: var(--tp-theme-brown);
            border-color: var(--tp-theme-brown);
            color: var(--tp-common-white);
        }
    }
    &__style-green {
        & .tp-category-menu-content {
            display: block;
        }
        .tp-offcanvas-category-toggle {
            background-color: var(--tp-theme-green);
        }

        .offcanvas__category ul li:hover > a {
            color: var(--tp-theme-green);
        }

        .offcanvas__close-btn:hover {
            background-color: var(--tp-theme-green);
        }

        .mean-container .mean-nav ul li.has-dropdown:hover > a,
        .mean-container .mean-nav ul li.has-dropdown:hover .mean-expand {
            color: var(--tp-theme-green);
        }

        .mean-container .mean-nav ul li.dropdown-opened > a,
        .mean-container .mean-nav ul li a:hover,
        .mean-container .mean-nav ul li.dropdown-opened > a.mean-expand.mean-clicked,
        .mean-container .mean-nav ul li a.mean-expand:hover i,
        .mean-container .mean-nav ul li a.mean-expand.mean-clicked:hover i,
        .mean-container .mean-nav ul li.dropdown-opened > a.mean-expand.mean-clicked i,
        .offcanvas__lang-list li:hover,
        .offcanvas__currency-list li:hover {
            color: var(--tp-theme-green);
        }

        & .offcanvas__btn .tp-btn-2:hover {
            background-color: var(--tp-theme-green);
            border-color: var(--tp-theme-green);
            color: var(--tp-common-white);
        }

        .tp-main-menu-mobile ul li:hover > a {
            color: var(--tp-theme-green);
        }

        .tp-main-menu-mobile ul li:hover > a .dropdown-toggle-btn i {
            color: var(--tp-theme-green);
        }
        .tp-main-menu-mobile ul li.has-dropdown > a.expanded {
            color: var(--tp-theme-green);
        }
        .tp-main-menu-mobile ul li.has-dropdown > a.expanded .dropdown-toggle-btn.dropdown-opened {
            background-color: var(--tp-theme-green);
            border-color: var(--tp-theme-green);
            color: var(--tp-common-white);
        }

        .tp-main-menu-mobile ul li.has-dropdown > a .dropdown-toggle-btn:hover {
            background-color: var(--tp-theme-green);
            border-color: var(--tp-theme-green);
            color: var(--tp-common-white);
        }

        .tp-menu-showcase-btn {
            border-radius: 0;
            background-color: var(--tp-theme-green);
        }

        .home-menu-title a:hover {
            color: var(--tp-theme-green);
        }

        .home-menu-title a {
            border-bottom: 0 !important;
        }

        // category css start

        .tp-category-mobile-menu ul li.has-dropdown > a .dropdown-toggle-btn:hover {
            background-color: var(--tp-theme-green);
            border-color: var(--tp-theme-green);
            color: var(--tp-common-white);
        }
        .tp-category-mobile-menu ul li:hover > a .dropdown-toggle-btn i {
            color: var(--tp-theme-green);
        }
        .tp-category-mobile-menu ul li.has-dropdown > a.expanded {
            color: var(--tp-theme-green);
        }
        .tp-category-mobile-menu ul li.has-dropdown > a.expanded .dropdown-toggle-btn.dropdown-opened {
            background-color: var(--tp-theme-green);
            border-color: var(--tp-theme-green);
            color: var(--tp-common-white);
        }
    }
}

.tp-offcanvas-category-toggle {
    font-size: 16px;
    color: var(--tp-common-white);
    display: block;
    position: relative;
    width: 100%;
    text-align: left;
    background-color: var(--tp-theme-primary);
    padding: 10px 20px;
    &::after {
        content: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M6 9l6 6l6 -6" /></svg>');
        position: absolute;
        right: 20px;
        top: calc(50% + 3px);
        @extend %translateY1_2;
    }
    & svg,
    & i {
        margin-right: 14px;
        font-size: 16px;
    }
}
