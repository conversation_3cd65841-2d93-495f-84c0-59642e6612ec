@use '../../utils' as *;

/*----------------------------------------*/
/*  8.21 Checkout CSS
/*----------------------------------------*/

.#{$theme-prefix}-checkout {
    &-verify {
        &-item {
            margin-bottom: 40px;
            &:not(:last-child) {
                margin-bottom: 14px;
            }
        }
        &-reveal {
            font-size: 14px;
            color: var(--tp-common-black);
            margin-bottom: 0;
            display: inline-block;
            border: 1px dashed #aab0b2;
            padding: 8px 26px;
            width: 100%;

            & button {
                color: var(--tp-theme-primary);
                position: relative;

                &::after {
                    position: absolute;
                    content: '';
                    left: 0;
                    bottom: 4px;
                    width: 100%;
                    height: 1px;
                    background-color: var(--tp-theme-primary);
                }
            }
        }
    }
    &-btn {
        display: inline-block;
        font-size: 16px;
        font-weight: 500;
        color: var(--tp-common-white);
        background-color: var(--tp-theme-primary);
        height: 50px;
        line-height: 50px;
        padding: 0 30px;
        text-align: center;
        &:hover {
            background-color: var(--tp-common-black);
            color: var(--tp-common-white);
        }
    }
    &-input {
        margin-bottom: 20px;
        & label {
            font-weight: 500;
            color: var(--tp-common-black);

            & span {
                color: red;
            }
        }
        & input,
        & textarea {
            width: 100%;
            background: #ffffff;
            border: 1px solid #d5d8db;
            color: var(--tp-common-black);
            height: 50px;

            &:focus {
                border-color: var(--tp-common-black);
            }
        }
        & textarea {
            height: 200px;
            resize: none;
        }

        & .nice-select {
            height: 50px;
            line-height: 48px;
            border-radius: 0;
            border: 1px solid #d5d8db;
            color: var(--tp-common-black);
            float: none;
            padding: 0 26px;

            &::after {
                right: 26px;
            }

            &.open {
                & .list {
                    @include transform(scale(1) translateY(0px));
                }
            }

            & .list {
                margin-top: 0;
                border-radius: 0;
                transform-origin: center center;
                @include transform(scale(0.9) translateY(0px));
                width: 100%;
                padding: 12px 0;
                & .option {
                    line-height: 1.2;
                    min-height: inherit;
                    padding-top: 5px;
                    padding-bottom: 5px;
                    &:hover {
                        color: var(--tp-theme-primary);
                    }
                }
            }
        }
    }
    &-option {
        margin-bottom: 4px;
        &-wrapper {
            margin-top: 10px;
            margin-bottom: 17px;
        }
        & input {
            display: none;
            &:checked {
                & ~ label {
                    &::after {
                        background-color: var(--tp-theme-primary);
                        border-color: var(--tp-theme-primary);
                    }
                    &::before {
                        visibility: visible;
                        opacity: 1;
                    }
                }
            }
        }

        & label {
            font-size: 15px;
            color: #55585b;
            position: relative;
            padding-left: 26px;
            z-index: 1;
            &::after {
                position: absolute;
                content: '';
                top: 4px;
                left: 0;
                width: 18px;
                height: 18px;
                line-height: 16px;
                text-align: center;
                border: 1px solid #c3c7c9;
                z-index: -1;
                @include tp-transition(all, 0.2s);
            }
            &::before {
                position: absolute;
                content: url('../images/icons/check.svg');
                top: 4px;
                left: 0;
                width: 18px;
                height: 18px;
                line-height: 16px;
                text-align: center;
                visibility: hidden;
                opacity: 0;
                color: var(--tp-common-white);
                @include tp-transition(all, 0.2s);
            }

            & a {
                &:hover {
                    color: var(--tp-theme-primary);
                }
            }

            &:hover {
                cursor: pointer;
            }
        }
    }
    &-bill {
        &-area {
            padding: 45px 40px 24px;
            background-color: var(--tp-common-white);

            @media #{$xs} {
                padding: 45px 25px 24px;
            }
        }
        &-title {
            font-weight: 600;
            font-size: 26px;
            margin-bottom: 35px;
        }
    }
    &-place {
        // place order
        padding: 44px 50px 50px;

        @media #{$lg} {
            padding: 35px 30px;
        }
        @media #{$md, $sm, $xs} {
            margin-top: 50px;
        }
        &-title {
            font-size: 26px;
            font-weight: 600;
            margin-right: 37px;
        }
    }
    &-payment {
        padding-bottom: 26px;
        margin-bottom: 24px;
        border-bottom: 1px solid #e0e2e3;
        &-item {
            &:not(:last-child) {
                margin-bottom: 10px;
            }
            &.paypal-payment {
                & label {
                    @include flexbox();
                    align-items: center;
                    flex-wrap: wrap;
                }
            }
            & input {
                display: none;

                &:checked {
                    & ~ label {
                        &::after {
                            border-color: var(--tp-theme-primary);
                        }
                        &::before {
                            opacity: 1;
                            visibility: visible;
                        }
                    }
                }
            }
            & label {
                font-size: 14px;
                position: relative;
                padding-left: 27px;
                font-weight: 500;
                color: var(--tp-common-black);

                &:hover {
                    cursor: pointer;
                }

                &::after {
                    position: absolute;
                    content: '';
                    left: 0;
                    top: 5px;
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    border: 1px solid #bcbcbc;
                    @include tp-transition(all, 0.2s);
                }
                &::before {
                    position: absolute;
                    content: '';
                    left: 3px;
                    top: 8px;
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    background-color: var(--tp-theme-primary);
                    visibility: hidden;
                    opacity: 0;
                    @include tp-transition(all, 0.2s);
                }

                & img {
                    margin-left: 14px;
                    @include transform(translateY(-2px));
                }

                & a {
                    margin-left: 20px;
                    position: relative;
                    &::after {
                        position: absolute;
                        content: '';
                        left: 0;
                        bottom: 3px;
                        width: 100%;
                        height: 1px;
                        background-color: var(--tp-common-black);
                    }
                }
            }
        }
        &-desc {
            position: relative;
            padding-top: 10px;
            display: none;

            &::after {
                position: absolute;
                content: '';
                left: 57px;
                top: 0;
                width: 16px;
                height: 16px;
                background-color: #f6f7f9;
                @include transform(translateY(3px) rotate(45deg));
            }
            & p {
                background-color: #f6f7f9;
                padding: 19px 30px;
                margin-left: 27px;
                font-size: 14px;
                line-height: 1.57;
                color: #55585b;
                margin-bottom: 0;
            }
        }
    }
    &-agree {
        margin-bottom: 24px;
    }
}

.#{$theme-prefix}-return-customer {
    display: none;
    margin-top: 14px;
    padding: 40px 40px;
    background-color: var(--tp-common-white);
    &-input {
        margin-bottom: 20px;
        & label {
            font-weight: 500;
            color: var(--tp-common-black);

            & span {
                color: red;
            }
        }
        & input {
            width: 100%;
            background: #ffffff;
            border: 1px solid #d5d8db;
            color: var(--tp-common-black);
            height: 50px;
        }
    }
    &-remeber {
        & input {
            display: none;
            &:checked {
                & ~ label {
                    &::after {
                        background-color: var(--tp-theme-primary);
                        border-color: var(--tp-theme-primary);
                    }
                    &::before {
                        visibility: visible;
                        opacity: 1;
                    }
                }
            }
        }

        & label {
            font-size: 15px;
            color: #55585b;
            position: relative;
            padding-left: 26px;
            z-index: 1;
            &::after {
                position: absolute;
                content: '';
                top: 4px;
                left: 0;
                width: 18px;
                height: 18px;
                line-height: 16px;
                text-align: center;
                border: 1px solid #c3c7c9;
                z-index: -1;
                @include tp-transition(all, 0.2s);
            }
            &::before {
                position: absolute;
                content: url('../images/icons/check.svg');
                top: 4px;
                left: 0;
                width: 18px;
                height: 18px;
                line-height: 16px;
                text-align: center;
                visibility: hidden;
                opacity: 0;
                color: var(--tp-common-white);
                @include tp-transition(all, 0.2s);
            }

            & a {
                &:hover {
                    color: var(--tp-theme-primary);
                }
            }

            &:hover {
                cursor: pointer;
            }
        }
    }
    &-forgot {
        & a {
            font-weight: 400;
            font-size: 15px;
            color: var(--tp-theme-primary);
            position: relative;
            display: inline-block;

            &::after {
                position: absolute;
                content: '';
                left: auto;
                right: 0;
                bottom: 4px;
                width: 0%;
                height: 1px;
                background-color: var(--tp-theme-primary);
                @extend %tp-transition;
            }

            &:hover {
                &::after {
                    left: 0;
                    right: auto;
                    width: 100%;
                }
            }
        }
    }
}
