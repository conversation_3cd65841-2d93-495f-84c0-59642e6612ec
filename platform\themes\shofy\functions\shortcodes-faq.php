<?php

use Bo<PERSON>ble\Base\Forms\FieldOptions\CheckboxFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextareaFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\UiSelectorFieldOption;
use Botble\Base\Forms\Fields\CheckboxField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\Fields\UiSelectorField;
use Bo<PERSON><PERSON>\Faq\Models\Faq;
use Botble\Faq\Models\FaqCategory;
use Bo<PERSON>ble\Shortcode\Compilers\Shortcode as ShortcodeCompiler;
use Bo<PERSON>ble\Shortcode\Facades\Shortcode;
use Botble\Shortcode\Forms\ShortcodeForm;
use Bo<PERSON>ble\Theme\Facades\Theme;
use Illuminate\Support\Arr;

if (is_plugin_active('faq')) {
    Shortcode::register('faqs', __('FAQs'), __('FAQs'), function (ShortcodeCompiler $shortcode): ?string {
        $categoryIds = Shortcode::fields()->parseIds($shortcode->category_ids);

        if (empty($categoryIds)) {
            return null;
        }

        $style = in_array($shortcode->style, ['list', 'group']) ? $shortcode->style : 'list';

        $faqs = collect();
        $categories = collect();

        if ($style === 'list') {
            $faqs = Faq::query()
                ->wherePublished()
                ->whereIn('category_id', $categoryIds)
                ->get();
        } else {
            $categories = FaqCategory::query()
                ->wherePublished()
                ->whereIn('id', $categoryIds)
                ->with([
                    'faqs' => function ($query): void {
                        $query->wherePublished();
                    },
                ])
                ->get();
        }

        return Theme::partial('shortcodes.faqs.index', compact('shortcode', 'faqs', 'categories'));
    });

    Shortcode::setPreviewImage('faqs', Theme::asset()->url('images/shortcodes/faqs/group.png'));

    Shortcode::setAdminConfig('faqs', function (array $attributes): ShortcodeForm {
        $categories = FaqCategory::query()
            ->wherePublished()
            ->pluck('name', 'id')
            ->all();

        $categoryIds = explode(',', Arr::get($attributes, 'category_ids', ''));

        return ShortcodeForm::createFromArray($attributes)
            ->add(
                'style',
                UiSelectorField::class,
                UiSelectorFieldOption::make()
                    ->label(__('Style'))
                    ->numberItemsPerRow(2)
                    ->defaultValue('list')
                    ->selected(Arr::get($attributes, 'style', 'list'))
                    ->choices([
                        'list' => [
                            'image' => Theme::asset()->url('images/shortcodes/faqs/list.png'),
                            'label' => __('List'),
                        ],
                        'group' => [
                            'image' => Theme::asset()->url('images/shortcodes/faqs/group.png'),
                            'label' => __('Group by category'),
                        ],
                    ]),
            )
            ->add(
                'title',
                TextField::class,
                TextFieldOption::make()
                    ->label(__('Title')),
            )
            ->add(
                'description',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(__('Description'))
            )
            ->add(
                'category_ids',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(__('FAQ categories'))
                    ->choices($categories)
                    ->selected($categoryIds)
                    ->searchable()
                    ->multiple()
            )
            ->add(
                'expand_first_time',
                CheckboxField::class,
                CheckboxFieldOption::make()
                    ->label(__('Expand the content of the first FAQ'))
                    ->defaultValue(true)
            );
    });
}
