<?php

namespace Bo<PERSON>ble\SimpleSlider\Http\Controllers\Settings;

use Bo<PERSON>ble\Setting\Http\Controllers\SettingController;
use <PERSON><PERSON>ble\SimpleSlider\Forms\Settings\SimpleSliderSettingForm;
use Bo<PERSON>ble\SimpleSlider\Http\Requests\Settings\SimpleSliderSettingRequest;

class SimpleSliderSettingController extends SettingController
{
    public function edit()
    {
        $this->pageTitle(trans('plugins/simple-slider::simple-slider.settings.title'));

        return SimpleSliderSettingForm::create()->renderForm();
    }

    public function update(SimpleSliderSettingRequest $request)
    {
        return $this->performUpdate($request->validated());
    }
}
