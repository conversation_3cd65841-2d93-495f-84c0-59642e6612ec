<?php

namespace Bo<PERSON>ble\RequestLog;

use <PERSON><PERSON><PERSON>\Dashboard\Models\DashboardWidget;
use Bo<PERSON>ble\PluginManagement\Abstracts\PluginOperationAbstract;
use <PERSON><PERSON>ble\Widget\Models\Widget;
use Illuminate\Support\Facades\Schema;

class Plugin extends PluginOperationAbstract
{
    public static function remove(): void
    {
        Schema::dropIfExists('request_logs');

        Widget::query()
            ->where('widget_id', 'widget_request_errors')
            ->each(fn (DashboardWidget $dashboardWidget) => $dashboardWidget->delete());
    }
}
