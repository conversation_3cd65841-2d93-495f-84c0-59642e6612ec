<?php
    $hasLogoLight ??= false;
    $defaultIsDark ??= true;

    $logo = Theme::getLogo();
    $logoLight = Theme::getLogo('logo_light');

    $height = theme_option('logo_height', 35);
    $attributes = [
        'style' => sprintf('height: %s', is_numeric($height) ? "{$height}px" : $height),
        'loading' => 'eager',
    ];
?>

<?php if($logo || $logoLight): ?>
    <div class="logo">
        <a href="<?php echo e(BaseHelper::getHomepageUrl()); ?>">
            <?php if($hasLogoLight): ?>
                <?php echo e(RvMedia::image($logoLight ?: $logo, Theme::getSiteTitle(), attributes: ['class' => 'logo-light', ...$attributes], lazy: false)); ?>

                <?php echo e(RvMedia::image($logo ?: $logoLight, Theme::getSiteTitle(), attributes: ['class' => 'logo-dark', ...$attributes], lazy: false)); ?>

            <?php else: ?>
                <?php echo e(RvMedia::image($defaultIsDark ? $logo : $logoLight, Theme::getSiteTitle(), attributes: $attributes, lazy: false)); ?>

            <?php endif; ?>
        </a>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/partials/header/logo.blade.php ENDPATH**/ ?>