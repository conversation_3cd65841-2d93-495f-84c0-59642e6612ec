@if ($payment)
    <br>
    <p>{{ trans('plugins/payment::payment.amount') }}: {{ $payment->amount / 100 }} {{ $payment->currency }}</p>
    <p>{{ trans('plugins/payment::payment.email') }}: {{ $payment->email }}</p>
    <p>{{ trans('plugins/payment::payment.phone') }}: {{ $payment->contact }}</p>
    <hr>

    @if ($payment->amount_refunded)
        <h6 class="alert-heading">{{ trans('plugins/payment::payment.amount_refunded') }}:
            {{ $payment->amount_refunded / 100 }} {{ $payment->currency }}
        </h6>
    @endif

    @if ($refunds = Arr::get($paymentModel->metadata, 'refunds', []))
        @foreach ($refunds as $refund)
            <div id="{{ Arr::get($refund, 'data.id') }}">
                @include('plugins/razorpay::refund-detail')
            </div>
        @endforeach
    @endif

    @include('plugins/payment::partials.view-payment-source')
@endif
