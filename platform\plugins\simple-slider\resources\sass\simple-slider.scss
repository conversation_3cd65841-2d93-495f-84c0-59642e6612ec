.owl-carousel {
    position: relative;

    .owl-item {
        img {
            width: 100%;
        }
    }
}

.carousel--nav {
    position: relative;
    z-index: 10;

    .owl-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 20;
        width: 100%;
        height: 0;
        display: none;

        > * {
            display: inline-block;
            position: relative;
            vertical-align: top;
            width: 50px;
            height: 50px;
            background-color: transparent;
            transition: all 0.4s ease;
            transform: translateY(-50%);
        }

        > * i {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 30px;
            color: #282828;
        }

        > :hover {
            box-shadow: 0 0 10px 5px rgba(0, 0, 0, 0.1);
            background-color: #ffffff;
        }

        .owl-prev {
            margin-left: 40px;
        }

        .owl-next {
            float: right;
            margin-right: 40px;
        }
    }

    .owl-dots {
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 100%;
        text-align: center;

        .owl-dot {
            display: inline-block;
            width: 10px;
            height: 10px;
            margin-right: 8px;
            border: 1px solid #bebebe;
            background-color: transparent;
            border-radius: 50%;

            &:last-child {
                margin-right: 0;
            }

            &.active {
                background-color: #000000;
                border-color: #000000;
            }
        }
    }

    &.inside {
        .owl-nav {
            z-index: 20;
            height: 0;

            > * {
                transform: translateY(-50%);
            }
        }

        .owl-prev {
            margin-left: 0;

            i {
                padding-right: 5px;
            }
        }

        .owl-next {
            float: right;
            margin-right: 0;

            i {
                padding-left: 5px;
            }
        }
    }
}

@media (min-width: 1200px) {
    .carousel--nav {
        .owl-nav {
            display: block;
        }
    }
}

.slider-item {
    position: relative;
    max-height: 400px;
    overflow: hidden;
    margin: 10px 0;

    .slider-item-header {
        position: absolute;
        bottom: 0;
        left: 0;
        width: calc(100% - 40px);
        padding: 20px;
        z-index: 20;
        background-color: rgba(0, 0, 0, 0.6);

        .slider-item-title {
            color: #ffffff;
            font-size: 16px;
            font-weight: 700;
            text-transform: uppercase;
        }

        .slider-item-description {
            color: #eeeeee;
        }

        .slider-item-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10;
        }
    }
}
