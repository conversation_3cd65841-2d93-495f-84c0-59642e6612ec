<div class="tp-product-categories-slider swiper-container">
    <div class="swiper-wrapper">
        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="swiper-slide">
                <div class="tp-product-category-item text-center mb-40">
                    <div class="tp-product-category-thumb fix">
                        <a href="<?php echo e($category->url); ?>" title="<?php echo e($category->name); ?>">
                            <?php echo e(RvMedia::image($category->image, $category->name)); ?>

                        </a>
                    </div>
                    <div class="tp-product-category-content">
                        <h3 class="tp-product-category-title">
                            <a href="<?php echo e($category->url); ?>" title="<?php echo e($category->name); ?>"><?php echo e($category->name); ?></a>
                        </h3>
                        <p>
                            <?php if($category->count_all_products === 1): ?>
                                <?php echo e(__('1 product')); ?>

                            <?php else: ?>
                                <?php echo e(__(':count products', ['count' => number_format($category->count_all_products)])); ?>

                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/widgets/product-categories/templates/styles/slider.blade.php ENDPATH**/ ?>