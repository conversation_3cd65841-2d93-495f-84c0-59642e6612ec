<?php
    $slidesToShow = $shortcode->slides_to_show ?: 4;

    if ($shortcode->with_sidebar) {
        $slidesToShow = $slidesToShow - 1;
    }

    $sliderUniqueId = 'tp-arrival-slider-' . uniqid();
?>

<section class="tp-product-arrival-area pt-30 pb-30"
     <?php if($shortcode->background_color): ?>
         style="background-color: <?php echo e($shortcode->background_color); ?> !important;"
    <?php endif; ?>
>
    <div class="container">
        <div class="tp-product-arrival-wrapper">
            <div class="row align-items-center mb-40">
                <div class="col-xl-5 col-sm-6">
                    <?php echo Theme::partial('section-title', compact('shortcode')); ?>

                </div>
                <div class="col-xl-7 col-sm-6">
                    <div class="tp-product-arrival-more-wrapper d-flex justify-content-end">
                        <div class="tp-product-arrival-arrow tp-swiper-arrow text-end tp-product-arrival-border">
                            <button type="button" class="tp-arrival-slider-button-prev tp-arrival-prev-<?php echo e($sliderUniqueId); ?>">
                                <svg width="8" height="14" viewBox="0 0 8 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7 13L1 7L7 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button type="button" class="tp-arrival-slider-button-next tp-arrival-next-<?php echo e($sliderUniqueId); ?>">
                                <svg width="8" height="14" viewBox="0 0 8 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M1 13L7 7L1 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        <?php if($shortcode->with_sidebar): ?>
            <div class="row">
                <div class="col-xl-4 col-lg-5">
                    <?php echo $__env->make(Theme::getThemeNamespace('partials.shortcodes.ecommerce-products.partials.sidebar'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
                <div class="col-xl-8 col-lg-7">
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-xl-12">
                            <div class="tp-product-arrival-slider fix">
                                <div class="tp-product-arrival-active swiper-container"
                                     id="<?php echo e($sliderUniqueId); ?>"
                                     data-items-per-view="<?php echo e($slidesToShow); ?>"
                                     data-slider-id="<?php echo e($sliderUniqueId); ?>">
                                    <div class="swiper-wrapper">
                                        <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.product-item'), ['class' => 'swiper-slide'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if($shortcode->with_sidebar): ?>
                </div>
            </div>
        <?php endif; ?>
        </div>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/partials/shortcodes/ecommerce-products/slider.blade.php ENDPATH**/ ?>