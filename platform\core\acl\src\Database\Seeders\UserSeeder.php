<?php

namespace Bo<PERSON>ble\ACL\Database\Seeders;

use <PERSON><PERSON>ble\ACL\Models\Role;
use Bo<PERSON>ble\ACL\Models\User;
use Bo<PERSON>ble\ACL\Services\ActivateUserService;
use Botble\Base\Supports\BaseSeeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;

class UserSeeder extends BaseSeeder
{
    public function run(): void
    {
        Schema::disableForeignKeyConstraints();

        User::query()->truncate();
        Role::query()->truncate();
        DB::table('role_users')->truncate();
        DB::table('activations')->truncate();

        $faker = $this->fake();

        $data = [
            'first_name' => $faker->firstName(),
            'last_name' => $faker->lastName(),
            'email' => $faker->companyEmail(),
            'username' => config('core.base.general.demo.account.username'),
            'password' => config('core.base.general.demo.account.password'),
            'super_user' => 1,
            'manage_supers' => 1,
        ];

        if (File::isDirectory(database_path('seeders/files/users'))) {
            $files = $this->uploadFiles('users');

            if ($files) {
                $data['avatar_id'] = $files[0]['data']->id;
            }
        }

        $superuser = $this->createUser($data);

        $permissions = (new Role())->getAvailablePermissions();

        $permissions = array_map(function () {
            return true;
        }, $permissions);

        Role::query()->forceCreate([
            'name' => 'Admin',
            'slug' => 'admin',
            'description' => 'Admin users role',
            'permissions' => $permissions,
            'is_default' => true,
            'created_by' => $superuser->getKey(),
            'updated_by' => $superuser->getKey(),
        ]);
    }

    protected function createUser(array $data): User
    {
        $user = new User();
        $user->forceFill($data);
        $user->save();

        app(ActivateUserService::class)->activate($user);

        return $user;
    }
}
