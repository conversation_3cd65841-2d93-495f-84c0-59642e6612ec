<?php
    $category = $categories->first();

    if ($categories->count() == 1 && $category) {
        $categories = $category->activeChildren;
    } else {
        $categories = $categories->slice(1);
    }
?>

<?php if($category || $ads): ?>
    <div class="tp-product-gadget-sidebar mb-40">
        <?php if($category): ?>
            <div class="tp-product-gadget-categories p-relative fix mb-10" <?php if($shortcode->border_color): ?> style="border-color: <?php echo e($shortcode->border_color); ?>" <?php endif; ?>>
                <?php if($image = ($shortcode->image ?: $category->image)): ?>
                    <div class="tp-product-gadget-thumb">
                        <?php echo e(RvMedia::image($image, $shortcode->title ?: $category->name, attributes: ['loading' => 'lazy'])); ?>

                    </div>
                <?php endif; ?>
                <h3 class="tp-product-gadget-categories-title">
                    <?php if($shortcode->title): ?>
                        <?php echo e($shortcode->title); ?>

                    <?php else: ?>
                        <a href="<?php echo e($category->url); ?>" title="<?php echo e($category->name); ?>"><?php echo e($category->name); ?></a>
                    <?php endif; ?>
                </h3>

                <?php if($categories->isNotEmpty()): ?>
                    <div class="tp-product-gadget-categories-list">
                        <ul>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><a href="<?php echo e($child->url); ?>"><?php echo e($child->name); ?></a></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if($actionLabel = $shortcode->action_label): ?>
                    <div class="tp-product-gadget-btn">
                        <a href="<?php echo e($shortcode->action_url ?: $category->url); ?>" class="tp-link-btn">
                            <?php echo e($actionLabel); ?>

                            <svg width="15" height="13" viewBox="0 0 15 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13.9998 6.19656L1 6.19656" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M8.75674 0.975394L14 6.19613L8.75674 11.4177" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php if($ads): ?>
            <div class="tp-product-gadget-banner">
                <div class="tp-product-gadget-banner-slider-active swiper-container">
                    <div class="swiper-wrapper">
                        <?php $__currentLoopData = $ads; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div
                                class="tp-product-gadget-banner-item swiper-slide include-bg"
                            >
                                <div class="tp-product-gadget-banner-content position-relative">
                                    <?php echo Theme::partial('shortcodes.ads.includes.item', ['item' => $ad]); ?>


                                    <?php
                                        $title = $ad->getMetaData('title', true);
                                        $subtitle = $ad->getMetaData('subtitle', true);
                                    ?>

                                    <?php if($title || $subtitle): ?>
                                        <div class="align-items-center content-overplace d-flex flex-column justify-content-center position-absolute">
                                            <?php if($subtitle): ?>
                                                <span class="tp-product-gadget-banner-price"><?php echo e($subtitle); ?></span>
                                            <?php endif; ?>

                                            <?php if($title): ?>
                                                <h3 class="tp-product-gadget-banner-title">
                                                    <?php if($ad->url): ?>
                                                        <a href="<?php echo e($ad->click_url); ?>" <?php if($ad->open_in_new_tab): ?> target="_blank" <?php endif; ?>>
                                                    <?php endif; ?>
                                                        <?php echo BaseHelper::clean($title); ?>

                                                    <?php if($ad->url): ?>
                                                        </a>
                                                    <?php endif; ?>
                                                </h3>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <div class="tp-product-gadget-banner-slider-dot tp-swiper-dot"></div>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/partials/shortcodes/ecommerce-products/partials/sidebar.blade.php ENDPATH**/ ?>