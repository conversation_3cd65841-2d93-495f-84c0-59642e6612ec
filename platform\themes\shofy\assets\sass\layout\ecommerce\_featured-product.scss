@use '../../utils' as *;

/*----------------------------------------*/
/*  8.7 Featured Product CSS START
/*----------------------------------------*/

.#{$theme-prefix}-featured {
    &-slider {
        margin-right: -660px;
        @media #{$sm, $xs} {
            margin-right: 0;
        }
        &-arrow {
            & button {
                font-size: 30px;
                color: #b3bac0;
                position: relative;
                &:not(:last-child) {
                    padding-right: 14px;
                    margin-right: 10px;
                    &::after {
                        position: absolute;
                        content: '';
                        right: 0;
                        top: 63%;
                        @extend %translateY1_2;
                        height: 30px;
                        width: 1px;
                        background-color: #d6d9e0;
                    }
                }
                &:hover {
                    color: var(--tp-common-black);
                }
            }
        }
    }
    &-item {
        min-height: 380px;
        padding: 87px 50px;
        @media #{$sm} {
            padding: 87px 35px;
        }
        @media #{$xs} {
            min-height: 280px;
            padding: 35px 35px;
        }
        .#{$theme-prefix}-product-rating-icon-2 {
            margin-bottom: 20px;
        }
    }
    &-thumb {
        @extend %bg-thumb;
        z-index: -1;
    }
    &-title {
        font-weight: 400;
        font-size: 36px;
        line-height: 1.17;
        margin-bottom: 7px;
        @media #{$xs} {
            font-size: 25px;
        }
        & a {
            &:hover {
                color: var(--secondary-color);
            }
        }
    }
    &-price {
        &-wrapper {
            line-height: 1;
            margin-bottom: 2px;
        }
        font-weight: 500;
        font-size: 20px;
        color: var(--tp-common-black);
        line-height: 1;
        &.new-price {
            color: var(--tp-common-black);
        }
        &.old-price {
            font-weight: 400;
            font-size: 14px;
            text-decoration-line: line-through;
            color: var(--tp-text-1);
        }
    }
}

.#{$theme-prefix}-featured {
    &-item-3 {
        padding: 0px 50px 40px;
        background-color: var(--tp-common-white);
        border: 1px solid var(--tp-border-4);
        margin-right: -1px;
        margin-top: -1px;

        @media #{$xs} {
            padding: 0px 30px 40px;
        }
    }
    &-thumb-3 {
        margin-bottom: 20px;

        & img {
            max-width: 100%;
        }
        &.has-translate {
            & img {
                @include transform(translateY(20px));
            }
        }
    }
    &-title-3 {
        font-weight: 400;
        font-size: 26px;
        line-height: 1.15;
        margin-bottom: 14px;

        & a {
            &:hover {
                color: var(--tp-theme-primary);
            }
        }
    }

    &-thumb-3 {
        min-height: 210px;
    }

    &-content-3 {
        & p {
            font-size: 16px;
            line-height: 1.38;
            margin-bottom: 19px;
        }
    }
    &-price-3 {
        & span {
            font-size: 20px;
            color: var(--tp-theme-primary);
        }
    }
}
