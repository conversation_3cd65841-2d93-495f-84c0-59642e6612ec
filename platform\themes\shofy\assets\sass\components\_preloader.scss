@use '../utils' as *;

/*----------------------------------------*/
/*  2.5 Preloader
/*----------------------------------------*/

#loading {
    background-color: #fff;
    height: 100%;
    width: 100%;
    position: fixed;
    z-index: 999999;
    margin-top: 0px;
    top: 0px;
}
#loading-center {
    width: 100%;
    height: 100%;
    position: relative;
}
#loading-center-absolute {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 25%;
    transform: translate(-50%, -50%);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    #loading-center-absolute {
        width: 40%;
    }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
    #loading-center-absolute {
        width: 40%;
    }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    #loading-center-absolute {
        width: 45%;
    }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
    #loading-center-absolute {
        width: 50%;
    }
}
@media (max-width: 575px) {
    #loading-center-absolute {
        width: 90%;
    }
}

#loading {
    background-color: #fff;
    height: 100%;
    width: 100%;
    position: fixed;
    z-index: 999999;
    margin-top: 0px;
    top: 0px;
}
#loading-center {
    width: 100%;
    height: 100%;
    position: relative;
}
#loading-center-absolute {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.#{$theme-prefix}-preloader {
    &-logo {
        width: 180px;
        height: 180px;
        line-height: 180px;
        position: relative;
        text-align: center;
        margin: auto;
    }
    &-circle {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        & svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            @include animation((tp-rotate 5s linear infinite));
            & circle {
                &:last-child {
                    stroke: var(--tp-theme-primary);
                    stroke-dashoffset: 0;
                    stroke-dasharray: 1128, 3150;
                    @include animation((tp-loading 4s linear infinite));
                    transform-origin: center center;

                    @at-root {
                        @include keyframes(tp-loading) {
                            0% {
                                stroke-dashoffset: 0;
                                stroke-dasharray: 0, 3150;
                            }
                            100% {
                                stroke-dashoffset: -1131;
                                stroke-dasharray: 1128, 3138;
                            }
                        }
                        @include keyframes(tp-rotate) {
                            0% {
                                @include transform(rotate(0deg));
                            }
                            100% {
                                @include transform(rotate(360deg));
                            }
                        }
                    }
                }
            }
        }
    }
    &-content {
        text-align: center;
    }
    &-title {
        font-weight: 500;
        line-height: 1;
        margin-bottom: 0;

        @media #{$xs} {
            font-size: 70px;
        }
    }
    &-subtitle {
        font-size: 16px;
        margin-bottom: 0;
        color: var(--tp-common-black);
    }
}
