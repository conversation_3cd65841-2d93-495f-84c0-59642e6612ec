<?php

namespace Bo<PERSON>ble\Translation\Http\Controllers;

use <PERSON><PERSON>ble\DataSynchronize\Exporter\Exporter;
use Bo<PERSON>ble\DataSynchronize\Http\Controllers\ExportController;
use Botble\Translation\Exporters\ThemeTranslationExporter;

class ExportThemeTranslationController extends ExportController
{
    protected function getExporter(): Exporter
    {
        return ThemeTranslationExporter::make();
    }
}
