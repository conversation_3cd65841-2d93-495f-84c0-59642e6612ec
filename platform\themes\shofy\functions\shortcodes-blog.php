<?php

use <PERSON><PERSON><PERSON>\Base\Forms\FieldOptions\NumberFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Bo<PERSON>ble\Base\Forms\Fields\NumberField;
use Bo<PERSON>ble\Base\Forms\Fields\SelectField;
use Bo<PERSON>ble\Base\Forms\Fields\TextField;
use Bo<PERSON>ble\Shortcode\Compilers\Shortcode as ShortcodeCompiler;
use Bo<PERSON>ble\Shortcode\Facades\Shortcode;
use Bo<PERSON>ble\Shortcode\Forms\ShortcodeForm;
use Botble\Theme\Facades\Theme;
use Illuminate\Database\Eloquent\Collection;

app()->booted(function (): void {
    if (! is_plugin_active('blog')) {
        return;
    }

    Shortcode::register('blog-posts', __('Blog Posts'), __('Blog Posts'), function (ShortcodeCompiler $shortcode) {
        $limit = (int) $shortcode->limit ?: 3;

        /**
         * @var Collection $posts
         */
        $posts = match ($shortcode->type) {
            'featured' => get_featured_posts($limit),
            'popular' => get_popular_posts($limit),
            default => get_recent_posts($limit),
        };

        if ($posts->isEmpty()) {
            return null;
        }

        $posts->loadMissing(['slugable', 'categories.slugable']);

        return Theme::partial('shortcodes.blog-posts.index', compact('shortcode', 'posts'));
    });

    Shortcode::setPreviewImage('blog-posts', Theme::asset()->url('images/shortcodes/blog-posts.png'));

    Shortcode::setAdminConfig('blog-posts', function (array $attributes) {
        return ShortcodeForm::createFromArray($attributes)
            ->withLazyLoading()
            ->columns()
            ->add(
                'title',
                TextField::class,
                TextFieldOption::make()->label(__('Title'))->colspan(2),
            )
            ->add(
                'type',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(__('Post type'))
                    ->choices([
                        'recent' => __('Recent'),
                        'featured' => __('Featured'),
                        'popular' => __('Popular'),
                    ])
                    ->defaultValue('recent')
                    ->colspan(2),
            )
            ->add(
                'limit',
                NumberField::class,
                NumberFieldOption::make()
                    ->label(__('Limit'))
                    ->helperText(__('Number of posts to show'))
                    ->defaultValue(3)
                    ->colspan(2),
            )
            ->add(
                'button_label',
                TextField::class,
                TextFieldOption::make()->label(__('Button Label'))->placeholder(__('Button view more label'))->toArray(
                ),
            )
            ->add(
                'button_url',
                TextField::class,
                TextFieldOption::make()
                    ->label(__('Button URL'))
                    ->placeholder(__('Button view more URL'))
                    ->helperText(__('Leave empty to link to the blog page'))
            );
    });
});
