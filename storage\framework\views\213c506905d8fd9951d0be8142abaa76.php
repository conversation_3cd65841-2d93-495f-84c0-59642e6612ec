<?php echo $__env->make(EcommerceHelper::viewPath('includes.product-price'), [
    'priceWrapperClassName' => 'tp-product-price-wrapper',
    'priceClassName' => 'tp-product-price new-price',
    'priceOriginalWrapperClassName' => '',
    'priceOriginalClassName' => 'tp-product-price old-price',
], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/views/ecommerce/includes/product/style-1/price.blade.php ENDPATH**/ ?>