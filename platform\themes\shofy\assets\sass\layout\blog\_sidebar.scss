@use '../../utils' as *;
/*----------------------------------------*/
/*  5.3 Sidebar css
/*----------------------------------------*/

.#{$theme-prefix}-sidebar {
    $self: &;

    &-wrapper {
        position: sticky;
        top: 120px;
        @media #{$md, $sm, $xs} {
            margin-top: 60px;
        }
    }

    &-ml--24 {
        margin-left: -24px;

        @media #{$lg, $md, $sm, $xs} {
            margin-left: 0;
        }
    }
    &-widget {
        &-title {
            font-size: 20px;
            font-weight: 500;
            margin-bottom: 14px;
        }

        & ul {
            & li {
                list-style: none;
                &:not(:last-child) {
                    margin-bottom: 10px;
                }
                & a {
                    position: relative;
                    padding-left: 16px;
                    font-size: 16px;
                    color: #55585b;
                    display: block;

                    & span {
                        float: right;
                    }

                    &::after {
                        position: absolute;
                        content: '';
                        width: 4px;
                        height: 4px;
                        background-color: #ced2d6;
                        border-radius: 50%;
                        left: 0;
                        top: 12px;
                    }

                    &:hover {
                        color: var(--tp-theme-primary);
                    }
                }

                & ul {
                    padding-left: 15px;
                    padding-top: 6px;
                }
            }
        }

        &.widget_categories {
            #{$self} {
                &-widget-content {
                    padding: 25px 25px 31px;
                    border: 1px solid #e0e2e3;
                }
            }
        }
    }
    &-search {
        &-input {
            position: relative;
            & input {
                border: 1px solid #e0e2e3;
                height: 50px;
                padding-inline-end: 50px;
                font-size: 16px;

                @include tp-placeholder {
                    color: #95999d;
                }
            }

            & button {
                position: absolute;
                top: 50%;
                inset-inline-end: 25px;
                color: var(--tp-common-black);
                font-size: 16px;
                @extend %translateY1_2;
                & svg {
                    @extend %tp-svg-y-2;
                }

                &:hover {
                    color: var(--tp-theme-primary);
                }
            }
        }
    }
    &-about {
        padding: 40px 40px 35px;
        text-align: center;
        border: 1px solid #e0e2e3;
        &-thumb {
            & img {
                width: 160px;
                height: 160px;
                border-radius: 50%;
                object-fit: cover;
            }
        }
        &-title {
            font-size: 22px;
            font-weight: 500;
            margin-bottom: 0;

            & a {
                &:hover {
                    color: var(--tp-theme-primary);
                }
            }
        }
        &-designation {
            color: #a0a2a4;
            font-size: 15px;
            display: inline-block;
            margin-bottom: 10px;
        }
        &-content {
            & p {
                font-size: 16px;
                line-height: 1.5;
                margin-bottom: 20px;
            }
        }
    }
}

// tagcloud

.tagcloud {
    padding-top: 24px;
    border-top: 1px solid #e0e2e3;
    & a {
        font-size: 15px;
        color: var(--tp-text-body);
        padding: 5px 13px;
        line-height: 1;
        display: inline-block;
        border: 1px solid #e6e7e8;
        margin-bottom: 8px;
        margin-inline-end: 4px;

        &:hover {
            background-color: var(--tp-theme-primary);
            border-color: var(--tp-theme-primary);
            color: var(--tp-common-white);
        }

        &:last-child {
            margin-bottom: 0;
        }
    }
}
