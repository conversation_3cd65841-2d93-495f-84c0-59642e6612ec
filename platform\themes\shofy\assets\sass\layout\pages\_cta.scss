@use '../../utils' as *;

/*----------------------------------------*/
/*  7.8 CTA CSS
/*----------------------------------------*/

.#{$theme-prefix}-cta {
    &-shape {
        &-1 {
            position: absolute;
            bottom: 0;
            left: 0;
            mix-blend-mode: luminosity;
        }
        &-2 {
            position: absolute;
            top: 0;
            right: 0;
            mix-blend-mode: luminosity;
        }
    }
    &-title {
        font-weight: 600;
        font-size: 54px;
        line-height: 1.19;
        color: var(--tp-common-white);
        margin-bottom: 25px;

        @media #{$xl} {
            font-size: 50px;
        }

        @media #{$md} {
            font-size: 45px;
        }

        @media #{$xs} {
            font-size: 35px;
        }
    }
    &-wrapper {
        z-index: 3;
    }

    &-thumb {
        position: absolute;
        bottom: -15%;
        right: 26%;
        overflow: hidden;
        display: inline-block;
        height: 470px;
        width: 312px;
        background-color: var(--tp-grey-2);
        border: 6px solid var(--tp-common-black);
        border-radius: 30px;
        z-index: 2;

        @media #{$xl} {
            right: 14%;
        }
        @media #{$lg} {
            right: 10%;
        }
        @media #{$md} {
            bottom: -6%;
            right: 4%;
        }
        @media #{$sm} {
            position: relative;
            right: -14%;
        }
        @media #{$xs} {
            position: relative;
            right: 8px;
        }

        & img {
            position: absolute;
            top: -12px;
            left: 0;
            @extend %tp-transition;
        }

        &-gradient {
            position: absolute;
            top: 27%;
            right: 22%;
            display: inline-block;
            width: 450px;
            height: 450px;
            border-radius: 50%;
            background-color: rgba($color: var(--primary-color-rgb), $alpha: 0.6);
            z-index: 1;

            @media #{$xl} {
                right: 8%;
            }

            @media #{$lg} {
                right: 4%;
            }
            @media #{$sm} {
                top: 63%;
                right: 14%;
            }
        }
    }
}

.tp-app-btn {
    &:not(:last-child) {
        margin-right: 14px;
    }
    & a {
        border-radius: 30px;
        padding: 7px 26px;

        &:hover {
            background-color: #fff;

            & .app-icon span,
            & .app-content span,
            & .app-content p {
                color: var(--tp-common-black);
            }
        }
    }
    & .google-btn {
        background: #010f1c;
    }
    & .apple-btn {
        background: #fff;

        & .app-icon {
            & span {
                color: var(--tp-common-black);
                @include tp-transition(all, 0.3s);
            }
        }

        & .app-content {
            & p,
            & span {
                color: var(--tp-common-black);
            }
        }

        &:hover {
            background-color: #182416;
            & .app-icon span,
            & .app-content span,
            & .app-content p {
                color: var(--tp-common-white);
            }
        }
    }
    & .app-icon {
        & span {
            font-size: 22px;
            color: var(--tp-common-white);
            @include tp-transition(all, 0.3s);
            & svg {
                @extend %tp-svg-y-2;
            }
        }
    }
    & .app-content {
        line-height: 1;
        & span {
            font-weight: 500;
            font-size: 10px;
            line-height: 0.8;
            text-transform: uppercase;
            color: var(--tp-common-white);
            display: inline-block;
            margin-bottom: 8px;
            @include tp-transition(color, 0.3s);
        }
        & p {
            font-weight: 500;
            font-size: 18px;
            line-height: 0.94;
            margin-bottom: 0;
            color: var(--tp-common-white);
            @include transform(translateY(-3px));
            @include tp-transition(color, 0.3s);
        }
    }
}
