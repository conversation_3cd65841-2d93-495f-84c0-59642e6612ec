@use '../../utils' as *;

/*----------------------------------------*/
/*  7.5 Instagram CSS
/*----------------------------------------*/

.#{$theme-prefix}-instagram {
    $self: &;

    &-item {
        border-radius: 8px;
        &::after {
            position: absolute;
            content: '';
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba($color: #0e1013, $alpha: 0.5);
            opacity: 0;
            visibility: hidden;
            @extend %tp-transition;
        }

        &:hover {
            &::after {
                visibility: visible;
                opacity: 1;
            }

            #{$self} {
                &-icon {
                    top: 50%;
                    visibility: visible;
                    opacity: 1;
                }
            }
        }

        &-2 {
            position: relative;
            &::after {
                position: absolute;
                content: '';
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba($color: $black, $alpha: 0.3);
                opacity: 0;
                visibility: hidden;
                @extend %tp-transition;
            }
            &:hover {
                &::after {
                    visibility: visible;
                    opacity: 1;
                }

                #{$self} {
                    &-icon-2 {
                        top: 50%;
                        visibility: visible;
                        opacity: 1;
                    }
                }
            }
        }
    }
    &-icon {
        position: absolute;
        top: 60%;
        left: 50%;
        @include transform(translate(-50%, -50%));
        z-index: 1;
        visibility: hidden;
        opacity: 0;
        @extend %tp-transition;

        & a {
            display: inline-block;
            width: 44px;
            height: 44px;
            text-align: center;
            line-height: 44px;
            color: var(--tp-common-white);
            border: 2px solid rgba($color: #fff, $alpha: 0.2);
            font-size: 18px;
            border-radius: 8px;
            &:hover {
                background-color: var(--tp-common-white);
                border-color: var(--tp-common-white);
                color: var(--tp-common-black);
            }
        }
        &-2 {
            position: absolute;
            top: 60%;
            left: 50%;
            @include transform(translate(-50%, -50%));
            z-index: 1;
            visibility: hidden;
            opacity: 0;
            @extend %tp-transition;
            & a {
                display: inline-block;
                width: 60px;
                height: 60px;
                text-align: center;
                line-height: 60px;
                color: var(--tp-common-black);
                font-size: 18px;
                border-radius: 50px;
                background-color: var(--tp-common-white);
                &:hover {
                    background-color: var(--tp-theme-primary);
                    color: var(--tp-common-white);
                }

                & i {
                    @include transform(translateY(1px));
                }
            }
        }
    }
    &-banner {
        height: 100%;
        width: 100%;
        background-color: var(--tp-common-white);
        border: 1px solid var(--tp-common-black);
        @include flexbox();
        align-items: center;
        justify-content: center;
        flex-direction: column;

        @media #{$xs} {
            padding-top: 50px;
            padding-bottom: 50px;
        }

        &-icon {
            @media #{$lg} {
                margin-bottom: 20px;
            }
            & img {
                @media #{$xl, $sm, $xs} {
                    width: 50%;
                }
                @media #{$lg} {
                    width: 35%;
                }
            }
        }
        &-content {
            & span {
                font-weight: 400;
                font-size: 20px;
                color: #021d35;
                display: block;
                margin-bottom: 5px;
            }
            & a {
                font-weight: 700;
                font-size: 36px;
                color: #021d35;
                &:hover {
                    color: var(--tp-theme-primary);
                }
            }
        }
    }
    &-style-3 {
        #{$self} {
            &-icon-2 {
                & a {
                    &:hover {
                        background-color: var(--tp-theme-primary);
                    }
                }
            }
        }
    }
    &-style-4 {
        #{$self} {
            &-icon-2 {
                & a {
                    border: 2px solid rgba($color: $white, $alpha: 0.6);
                    background-color: transparent;
                    color: var(--tp-common-white);
                    line-height: 56px;
                    &:hover {
                        background-color: var(--tp-common-white);
                        border-color: var(--tp-common-white);
                        color: var(--tp-common-black);
                    }
                }
            }
        }
    }
}
