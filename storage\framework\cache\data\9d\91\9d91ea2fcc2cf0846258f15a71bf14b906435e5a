1751222067O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:7:{i:0;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:13;s:4:"name";s:10:"Headphones";s:9:"parent_id";i:12;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:0;s:5:"image";s:29:"main/product-categories/1.png";s:11:"is_featured";i:1;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:5;}s:11:" * original";a:13:{s:2:"id";i:13;s:4:"name";s:10:"Headphones";s:9:"parent_id";i:12;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:0;s:5:"image";s:29:"main/product-categories/1.png";s:11:"is_featured";i:1;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:5;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:64;s:3:"key";s:10:"headphones";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:13;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:64;s:3:"key";s:10:"headphones";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:13;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:14;s:4:"name";s:19:"Wireless Headphones";s:9:"parent_id";i:12;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:1;s:5:"image";s:44:"main/product-categories/category-thumb-1.jpg";s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:03";s:10:"updated_at";s:19:"2025-06-25 22:46:03";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:4;}s:11:" * original";a:13:{s:2:"id";i:14;s:4:"name";s:19:"Wireless Headphones";s:9:"parent_id";i:12;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:1;s:5:"image";s:44:"main/product-categories/category-thumb-1.jpg";s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:03";s:10:"updated_at";s:19:"2025-06-25 22:46:03";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:4;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:65;s:3:"key";s:19:"wireless-headphones";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:14;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:65;s:3:"key";s:19:"wireless-headphones";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:14;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:12;s:4:"name";s:11:"Accessories";s:9:"parent_id";i:2;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:2;s:5:"image";s:34:"main/product-categories/menu-3.jpg";s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:9;}s:11:" * original";a:13:{s:2:"id";i:12;s:4:"name";s:11:"Accessories";s:9:"parent_id";i:2;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:2;s:5:"image";s:34:"main/product-categories/menu-3.jpg";s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:9;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:63;s:3:"key";s:11:"accessories";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:12;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:63;s:3:"key";s:11:"accessories";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:12;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:3;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:15;s:4:"name";s:13:"TWS Earphones";s:9:"parent_id";i:12;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:2;s:5:"image";s:44:"main/product-categories/category-thumb-6.jpg";s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:03";s:10:"updated_at";s:19:"2025-06-25 22:46:03";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:4;}s:11:" * original";a:13:{s:2:"id";i:15;s:4:"name";s:13:"TWS Earphones";s:9:"parent_id";i:12;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:2;s:5:"image";s:44:"main/product-categories/category-thumb-6.jpg";s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:03";s:10:"updated_at";s:19:"2025-06-25 22:46:03";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:4;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:66;s:3:"key";s:13:"tws-earphones";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:15;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:66;s:3:"key";s:13:"tws-earphones";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:15;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:4;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:11;s:4:"name";s:11:"CPU Coolers";s:9:"parent_id";i:7;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:3;s:5:"image";s:44:"main/product-categories/category-thumb-9.jpg";s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:7;}s:11:" * original";a:13:{s:2:"id";i:11;s:4:"name";s:11:"CPU Coolers";s:9:"parent_id";i:7;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:3;s:5:"image";s:44:"main/product-categories/category-thumb-9.jpg";s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:7;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:62;s:3:"key";s:11:"cpu-coolers";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:11;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:62;s:3:"key";s:11:"cpu-coolers";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:11;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:5;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:16;s:4:"name";s:11:"Smart Watch";s:9:"parent_id";i:12;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:3;s:5:"image";s:29:"main/product-categories/4.png";s:11:"is_featured";i:1;s:10:"created_at";s:19:"2025-06-25 22:46:03";s:10:"updated_at";s:19:"2025-06-25 22:46:03";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:8;}s:11:" * original";a:13:{s:2:"id";i:16;s:4:"name";s:11:"Smart Watch";s:9:"parent_id";i:12;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:3;s:5:"image";s:29:"main/product-categories/4.png";s:11:"is_featured";i:1;s:10:"created_at";s:19:"2025-06-25 22:46:03";s:10:"updated_at";s:19:"2025-06-25 22:46:03";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:8;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:67;s:3:"key";s:11:"smart-watch";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:16;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:67;s:3:"key";s:11:"smart-watch";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:16;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:6;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:17;s:4:"name";s:14:"Gaming Console";s:9:"parent_id";i:2;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:3;s:5:"image";s:44:"main/product-categories/category-thumb-8.jpg";s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:03";s:10:"updated_at";s:19:"2025-06-25 22:46:03";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:4;}s:11:" * original";a:13:{s:2:"id";i:17;s:4:"name";s:14:"Gaming Console";s:9:"parent_id";i:2;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:3;s:5:"image";s:44:"main/product-categories/category-thumb-8.jpg";s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:03";s:10:"updated_at";s:19:"2025-06-25 22:46:03";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:4;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:68;s:3:"key";s:14:"gaming-console";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:17;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:68;s:3:"key";s:14:"gaming-console";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:17;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}