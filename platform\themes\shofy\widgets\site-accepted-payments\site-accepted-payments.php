<?php

use Bo<PERSON>ble\Base\Forms\FieldOptions\MediaImageFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Bo<PERSON>ble\Base\Forms\Fields\MediaImageField;
use Bo<PERSON>ble\Base\Forms\Fields\TextField;
use Bo<PERSON>ble\Widget\AbstractWidget;
use Bo<PERSON>ble\Widget\Forms\WidgetForm;

class SiteAcceptedPaymentsWidget extends AbstractWidget
{
    public function __construct()
    {
        parent::__construct([
            'name' => __('Site Accepted Payments'),
            'description' => __('Display accepted payments image or whatever image you want.'),
            'image' => null,
            'url' => null,
        ]);
    }

    protected function settingForm(): WidgetForm|string|null
    {
        return WidgetForm::createFromArray($this->getConfig())
            ->add(
                'image',
                MediaImageField::class,
                MediaImageFieldOption::make()
                    ->label(__('Image'))
            )
            ->add(
                'url',
                TextField::class,
                TextFieldOption::make()
                    ->label(__('URL'))
            );
    }
}
