@use '../utils' as *;
/*---------------------------------
    1.2 Common Classes
---------------------------------*/

.w-img {
    & img {
        width: 100%;
    }
}

.m-img {
    & img {
        max-width: 100%;
    }
}

.fix {
    overflow: hidden;
}
.clear {
    clear: both;
}

.z-index-1 {
    z-index: 1;
}

.z-index-11 {
    z-index: 11;
}

.overflow-y-visible {
    overflow-x: hidden;
    overflow-y: visible;
}

.p-relative {
    position: relative;
}
.p-absolute {
    position: absolute;
}

.include-bg {
    @include background();
}

// demo for method
.demo {
    @include tp-transition(color, 0.3s, linear);

    @include tp-transition-mul((color 0.3s linear, transform 0.2s ease));

    @include rtl {
        margin-left: 0;
        margin-right: 15px;
    }
    @include tp-root('div') {
        img {
            margin-left: 10px;
        }
    }
    @include dark {
        color: yellow;
    }
}

/*----------------------------------------
    Body Overlay
-----------------------------------------*/

.body-overlay {
    background-color: var(--tp-common-black);
    height: 100%;
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 9999;
    left: 0;
    opacity: 0;
    visibility: hidden;
    @include tp-transition();

    &:hover {
        cursor: url('../images/cross-out.png'), pointer;
    }
}

.body-overlay.opened {
    opacity: 0.7;
    visibility: visible;
}

/* dropcap */
.#{$theme-prefix}-dropcap {
    &::first-letter {
        font-size: 90px;
        font-weight: 500;
        float: left;
        text-align: center;
        color: var(--tp-common-black);
        margin-right: 5px;
        line-height: inherit;
    }
}

// for svg
// on hover stroke animation
// not checked
.class {
    stroke-dasharray: 189px, 191px;
    stroke-dashoffset: 0px;
}

/* gutter for x axis */
.tp-gx-20 {
    --bs-gutter-x: 20px;

    @media #{$lg} {
        --bs-gutter-x: 20px;
    }

    @media #{$md} {
        --bs-gutter-x: 20px;
    }

    @media #{$sm} {
        --bs-gutter-x: 15px;
    }

    @media #{$xs} {
        --bs-gutter-x: 10px;
    }

    & [class*='col-'] {
        padding-right: calc(var(--bs-gutter-x) * 0.5);
        padding-left: calc(var(--bs-gutter-x) * 0.5);
        margin-top: var(--bs-gutter-y);
    }
}

/* gutter for x axis */
.tp-gx-30 {
    --bs-gutter-x: 30px;

    @media #{$lg} {
        --bs-gutter-x: 30px;
    }

    @media #{$md} {
        --bs-gutter-x: 30px;
    }

    @media #{$sm} {
        --bs-gutter-x: 15px;
    }

    @media #{$xs} {
        --bs-gutter-x: 10px;
    }

    & [class*='col-'] {
        padding-right: calc(var(--bs-gutter-x) * 0.5);
        padding-left: calc(var(--bs-gutter-x) * 0.5);
        margin-top: var(--bs-gutter-y);
    }
}

/* gutter for x axis */
.tp-gx-40 {
    --bs-gutter-x: 40px;

    @media #{$lg} {
        --bs-gutter-x: 40px;
    }

    @media #{$md} {
        --bs-gutter-x: 30px;
    }

    @media #{$sm} {
        --bs-gutter-x: 30px;
    }

    @media #{$xs} {
        --bs-gutter-x: 15px;
    }

    & [class*='col-'] {
        padding-right: calc(var(--bs-gutter-x) * 0.5);
        padding-left: calc(var(--bs-gutter-x) * 0.5);
        margin-top: var(--bs-gutter-y);
    }
}
