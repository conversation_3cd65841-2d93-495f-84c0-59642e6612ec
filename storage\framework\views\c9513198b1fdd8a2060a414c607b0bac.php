<?php
    $breadcrumbStyle = Theme::get('breadcrumbStyle', theme_option('breadcrumb_style', 'align-start'));
    $breadcrumbBackground = RvMedia::getImageUrl(Theme::get('breadcrumbBackground', theme_option('breadcrumb_background_image')));
    $breadcrumbBackgroundColor = theme_option('breadcrumb_background_color');
    $breadcrumbHeight = Theme::get('breadcrumbHeight') ?: theme_option('breadcrumb_height');
    $breadcrumbHeight = is_numeric($breadcrumbHeight) ? "{$breadcrumbHeight}px" : $breadcrumbHeight;
    $breadcrumbClasses = Theme::get('breadcrumbClasses');
?>

<?php if(theme_option('theme_breadcrumb_enabled', true) && $breadcrumbStyle !== 'none'): ?>
    <section
        class="<?php echo \Illuminate\Support\Arr::toCssClasses([
            'breadcrumb__area include-bg',
            'pt-60 pb-60 mb-50' => $breadcrumbStyle !== 'without-title' && empty($breadcrumbHeight),
            'pt-30 pb-30 mb-50' => $breadcrumbStyle === 'without-title' && empty($breadcrumbHeight),
            'breadcrumb__style-2 include-bg' => $breadcrumbStyle === 'without-title',
            'pt-30 pb-30' => $breadcrumbStyle === 'without-title' && empty($breadcrumbHeight),
            'mb-30 text-center' => $breadcrumbStyle === 'align-center',
            'mb-30 text-start' => $breadcrumbStyle === 'align-start',
            'breadcrumb__padding' => $breadcrumbStyle === 'full-width',
            'pt-30' => empty($breadcrumbHeight) || $breadcrumbHeight < 90,
            $breadcrumbClasses ?? null,
        ]); ?>"
        style="<?php echo \Illuminate\Support\Arr::toCssStyles(["background-image: url($breadcrumbBackground)" => $breadcrumbBackground, "background-color: $breadcrumbBackgroundColor" => $breadcrumbBackgroundColor, "display: flex; align-items: center; height: $breadcrumbHeight" => $breadcrumbHeight]) ?>"
    >
        <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['container' => $breadcrumbStyle !== 'full-width', 'container-fluid' => $breadcrumbStyle === 'full-width']); ?>">
            <div class="breadcrumb__content p-relative z-index-1">
                <?php if(theme_option('breadcrumb_hide_title', 'no') == 'no' && $breadcrumbStyle !== 'without-title'): ?>
                    <h3 class="breadcrumb__title"><?php echo BaseHelper::clean(Theme::get('pageTitle') ? Theme::get('pageTitle') : SeoHelper::getTitleOnly()); ?></h3>
                <?php endif; ?>
                <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['breadcrumb__list', 'js_breadcrumb_reduce_length_on_mobile' => theme_option('breadcrumb_reduce_length_on_mobile', 'yes') == 'yes',]); ?>">
                    <?php $__currentLoopData = Theme::breadcrumb()->getCrumbs(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $crumb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <span>
                            <?php if($loop->last): ?>
                                <?php echo BaseHelper::clean($crumb['label']); ?>

                            <?php else: ?>
                                <a href="<?php echo e($crumb['url']); ?>"><?php echo BaseHelper::clean($crumb['label']); ?></a>
                            <?php endif; ?>
                        </span>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </section>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/partials/breadcrumbs.blade.php ENDPATH**/ ?>