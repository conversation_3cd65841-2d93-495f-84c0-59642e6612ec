({4736:function(){var t=this;$((function(){var e=$(".table-language");e.on("click",".delete-locale-button",(function(t){t.preventDefault(),$(".delete-crud-entry").data("url",$(t.currentTarget).data("url")),$(".modal-confirm-delete").modal("show")})),$(document).on("click",".delete-crud-entry",(function(o){o.preventDefault(),$(".modal-confirm-delete").modal("hide");var a=$(o.currentTarget).data("url");Botble.showButtonLoading($(t)),$httpClient.make().delete(a).then((function(t){var o=t.data;o.data&&(e.find("i[data-locale=".concat(o.data,"]")).unwrap(),$(".tooltip").remove()),e.find('.delete-locale-button[data-url="'.concat(a,'"]')).closest("tr").remove(),Botble.showSuccess(o.message)})).finally((function(){Botble.hideButtonLoading($(t))}))})),$(document).on("submit",".add-locale-form",(function(t){t.preventDefault(),t.stopPropagation();var o=$(this),a=o.find('button[type="submit"]');Botble.showButtonLoading(a),$httpClient.make().postForm(o.prop("action"),new FormData(o[0])).then((function(t){var o=t.data;Botble.showSuccess(o.message),e.load("".concat(window.location.href," .table-language > *"))})).finally((function(){Botble.hideButtonLoading(a)}))}))}))}})[4736]();