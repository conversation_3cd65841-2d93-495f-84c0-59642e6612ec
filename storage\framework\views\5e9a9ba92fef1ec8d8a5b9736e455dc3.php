<?php if($product instanceof \Botble\Ecommerce\Models\Product && $product->exists): ?>
    <div class="tp-product-sm-item d-flex align-items-center">
        <div class="tp-product-thumb mr-25 fix">
            <a href="<?php echo e($product->url); ?>">
                <?php echo e(RvMedia::image($product->image, $product->name, 'thumb')); ?>

            </a>
        </div>
        <div class="tp-product-sm-content">
            <?php if(is_plugin_active('marketplace') && $product->store?->id): ?>
                <div class="tp-product-category">
                    <a href="<?php echo e($product->store->url); ?>"><?php echo e($product->store->name); ?></a>
                </div>
            <?php endif; ?>

            <h3 class="tp-product-title">
                <a href="<?php echo e($product->url); ?>"><?php echo e($product->name); ?></a>
            </h3>

                <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['tp-product-price-review' => theme_option('product_listing_review_style', 'default') !== 'default' && EcommerceHelper::isReviewEnabled() && ($product->reviews_avg || theme_option('ecommerce_hide_rating_star_when_is_zero', 'no') === 'no')]); ?>">
                <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.product.style-1.rating'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.product.style-1.price'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/views/ecommerce/includes/product/style-1/small.blade.php ENDPATH**/ ?>