<section class="tp-category-area">
    <div class="container">
        <?php echo Theme::partial('section-title', compact('shortcode')); ?>


        <div class="row">
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-4 col-sm-6">
                    <div
                        class="tp-category-main-box mb-25 p-relative fix"
                        <?php if($shortcode->background_color): ?>
                            style="background-color: <?php echo e($shortcode->background_color); ?> !important;"
                        <?php endif; ?>
                    >
                        <a href="<?php echo e($category->url); ?>" title="<?php echo e($category->name); ?>">
                            <div
                                class="tp-category-main-thumb include-bg transition-3"
                                <?php if($category->image): ?>
                                    style="background: url('<?php echo e(RvMedia::getImageUrl($category->image)); ?>') no-repeat; background-size: cover;"
                                <?php endif; ?>
                            ></div>
                        </a>
                        <div class="tp-category-main-content">
                            <h3 class="tp-category-main-title">
                                <a href="<?php echo e($category->url); ?>" title="<?php echo e($category->name); ?>"><?php echo e($category->name); ?></a>
                            </h3>
                            <?php if($shortcode->show_products_count): ?>
                                <span class="tp-category-main-item">
                                    <?php if($category->count_all_products === 1): ?>
                                        <?php echo e(__('1 product')); ?>

                                    <?php else: ?>
                                        <?php echo e(__(':count products', ['count' => number_format($category->count_all_products)])); ?>

                                    <?php endif; ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/partials/shortcodes/ecommerce-categories/grid.blade.php ENDPATH**/ ?>