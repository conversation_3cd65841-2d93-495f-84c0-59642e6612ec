<?php
    $iconImage = $category->icon_image;
    $icon = $category->icon;
?>

<?php if($iconImage || $icon): ?>
    <span class="tp-product-category-icon">
        <?php if($iconImage): ?>
            <?php echo e(RvMedia::image($iconImage, $category->name, attributes: ['loading' => false, 'style' => 'width: 18px; height: 18px'])); ?>

        <?php elseif($icon): ?>
            <?php echo BaseHelper::renderIcon($icon); ?>

        <?php endif; ?>
    </span>
<?php endif; ?>

<?php echo e($category->name); ?>

<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/partials/header/categories-item.blade.php ENDPATH**/ ?>