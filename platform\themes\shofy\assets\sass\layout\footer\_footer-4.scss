@use '../../utils' as *;

/*----------------------------------------*/
/*  6.4 Footer Style 4
/*----------------------------------------*/

.#{$theme-prefix}-footer {
    $self: &;
    &-style-4 {
        #{$self} {
            &-widget {
                & ul {
                    & li {
                        & a {
                            &:hover {
                                color: var(--tp-common-black);
                                &::after {
                                    background-color: var(--tp-common-black);
                                }
                            }
                        }
                    }
                }
            }
            &-logo {
                margin-bottom: 13px;
            }
            &-social-4 {
                .tp-footer-social-title-4 {
                    font-weight: 500;
                    font-size: 16px;
                    line-height: 12px;
                    margin-bottom: 12px;
                }
                & a {
                    border-color: var(--tp-common-white);
                    box-shadow: 0 1px 2px rgba(1, 15, 28, 0.1);

                    &:hover {
                        color: var(--tp-common-white);
                        background-color: var(--tp-common-black);
                        border-color: var(--tp-common-black);
                        box-shadow: none;
                    }

                    img {
                        max-width: 38px;
                    }
                }
            }
        }
    }
    &-subscribe {
        & p {
            font-size: 15px;
        }
        &-input {
            position: relative;
            & input {
                height: 54px;
                background-color: var(--tp-common-white);
                border-color: var(--tp-common-white);
                box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.1);
                padding-right: 130px;
                @include tp-placeholder {
                    color: #7d7f82;
                }

                &:focus {
                    border-color: var(--tp-common-white);
                }
            }
            & button {
                position: absolute;
                top: 0;
                right: 0;
                z-index: 1;
                font-weight: 500;
                font-size: 16px;
                line-height: 23px;
                color: var(--tp-common-white);
                color: var(--tp-common-white);
                background-color: var(--tp-common-black);
                box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.1);
                padding: 16px 30px;
                height: 54px;

                &:hover {
                    background-color: var(--tp-theme-brown);
                }
            }
        }
    }
}

/* footer col design for home 4 */
.footer-col-4 {
    &-1 {
    }
    &-2 {
        padding-left: 23px;
        @media #{$xs} {
            padding-left: 0;
        }
    }
    &-3 {
        padding-left: 85px;
        @media #{$lg, $md,  $sm, $xs} {
            padding-left: 0;
        }
    }
    &-4 {
        padding-left: 48px;
        @media #{$lg, $md, $sm, $xs} {
            padding-left: 0;
        }
    }
}
