# Shofy E-commerce Application Setup Guide

## Overview
Shofy is a Laravel-based multivendor e-commerce marketplace platform with multiple themes and comprehensive features.

## Prerequisites
You need to install the following software on your Windows system:

### Required Software:
1. **PHP 8.2 or higher** - https://windows.php.net/download/
2. **Composer** - https://getcomposer.org/download/
3. **MySQL/MariaDB** or **XAMPP** (includes PHP, MySQL, Apache) - https://www.apachefriends.org/
4. **Node.js** ✅ (Already installed - v18.20.8)

## Setup Instructions

### Step 1: Install Required Software

#### Option A: Install XAMPP (Recommended for beginners)
1. Download and install XAMPP from https://www.apachefriends.org/
2. Start Apache and MySQL services from XAMPP Control Panel
3. Download Composer from https://getcomposer.org/download/

#### Option B: Individual installations
1. Install PHP 8.2+ from https://windows.php.net/download/
2. Install MySQL from https://dev.mysql.com/downloads/mysql/
3. Install Composer from https://getcomposer.org/download/

### Step 2: Setup Database
1. Open phpMyAdmin (if using XAMPP) or MySQL command line
2. Create a new database named `shofy`
3. Import one of the provided database files:
   - `database.sql` - Main database
   - `database-shofy-beauty.sql` - Beauty theme
   - `database-shofy-fashion.sql` - Fashion theme
   - `database-shofy-grocery.sql` - Grocery theme
   - `database-shofy-jewelry.sql` - Jewelry theme

### Step 3: Install Dependencies
```bash
# Install PHP dependencies
composer install

# Frontend dependencies are already installed ✅
# npm install (already completed)
```

### Step 4: Configure Environment
The `.env` file has been configured with default settings:
- Database: `shofy`
- URL: `http://localhost:8000`
- Debug mode: enabled

Update database credentials in `.env` if needed:
```
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=shofy
DB_USERNAME=root
DB_PASSWORD=
```

### Step 5: Generate Application Key
```bash
php artisan key:generate
```

### Step 6: Build Frontend Assets
```bash
npm run dev
# or for production:
npm run production
```

### Step 7: Run the Application
```bash
php artisan serve
```

The application will be available at: http://localhost:8000

## Admin Access
- Admin URL: http://localhost:8000/admin
- Default credentials will be in the imported database

## Available Themes
- Shofy (main)
- Shofy Beauty
- Shofy Fashion  
- Shofy Grocery
- Shofy Jewelry

## Features Included
- Multivendor marketplace
- Payment gateways (PayPal, Stripe, Razorpay, etc.)
- Blog, FAQ, Gallery
- Newsletter, Social login
- SEO tools, Analytics
- Multiple languages
- Admin panel

## Troubleshooting
1. If you get permission errors, ensure the `storage` and `bootstrap/cache` directories are writable
2. If database connection fails, check your MySQL service is running
3. For frontend issues, try `npm run dev` to rebuild assets

## Next Steps
After setup, you can:
1. Access the admin panel to configure the store
2. Set up payment gateways
3. Add products and categories
4. Configure themes and appearance
5. Set up vendor registration
