@use '../../utils' as *;

/*----------------------------------------*/
/*  7.11 Error CSS
/*----------------------------------------*/

.#{$theme-prefix}-error {
    &-title {
        font-size: 50px;
        font-weight: 500;

        @media #{$sm} {
            font-size: 40px;
        }

        @media #{$xs} {
            font-size: 30px;
        }
    }
    &-content {
        & p {
            font-size: 16px;
            line-height: 1.6;
            padding: 0 70px;
            margin-bottom: 27px;

            @media #{$xs} {
                padding: 0;
            }
        }
    }
    &-btn {
        font-size: 16px;
        color: var(--tp-common-white) !important;
        font-weight: 500;
        background-color: var(--tp-theme-primary);
        padding: 11px 30px;
        &:hover {
            background-color: var(--tp-common-black);
            color: var(--tp-common-white);
        }
    }
}
