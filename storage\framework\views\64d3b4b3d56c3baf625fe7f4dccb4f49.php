<section class="tp-feature-area tp-feature-border-radius pt-30 pb-30">
    <div class="container">
        <div class="row gx-1 gy-1 gy-xl-0">
            <?php $__currentLoopData = $tabs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tab): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="tp-feature-item d-flex align-items-start h-100">
                        <?php if(($icon = $tab['icon']) && BaseHelper::hasIcon($icon)): ?>
                            <div class="tp-feature-icon mr-15">
                               <span <?php if($shortcode->icon_color): ?> style="color: <?php echo e($shortcode->icon_color); ?>" <?php endif; ?>>
                                   <?php echo BaseHelper::renderIcon($icon); ?>

                               </span>
                            </div>
                        <?php endif; ?>
                        <div class="tp-feature-content">
                            <h3 class="tp-feature-title"><?php echo BaseHelper::clean($tab['title']); ?></h3>
                            <?php if($tab['description']): ?>
                                <p><?php echo BaseHelper::clean($tab['description']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/partials/shortcodes/site-features/style-1.blade.php ENDPATH**/ ?>