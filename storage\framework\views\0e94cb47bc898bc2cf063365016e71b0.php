<?php
    $style = theme_option('ecommerce_product_item_style', 1);
    $style = in_array($style, [1, 2, 3, 4, 5]) ? $style : 1;

    $layout ??= 'grid';
?>

<?php echo $__env->make(Theme::getThemeNamespace("views.ecommerce.includes.product.style-$style.$layout"), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/views/ecommerce/includes/product-item.blade.php ENDPATH**/ ?>