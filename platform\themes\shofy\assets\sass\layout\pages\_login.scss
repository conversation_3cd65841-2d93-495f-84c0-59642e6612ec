@use '../../utils' as *;

/*----------------------------------------*/
/*  7.9 Login CSS
/*----------------------------------------*/

.auth-card {
    box-shadow: 0px 30px 70px rgba(1, 15, 28, 0.1);
    background-color: var(--tp-common-white) !important;

    .auth-card__header {
        h3 {
            font-weight: 500;
            font-size: 30px !important;
            margin-bottom: 4px !important;
        }

        & p {
            font-weight: 400;
            font-size: 16px;
            color: #49535b !important;

            & a {
                color: var(--tp-theme-primary);
                position: relative;
                display: inline-block;

                &::after {
                    position: absolute;
                    content: '';
                    left: auto;
                    right: 0;
                    bottom: 4px;
                    width: 0%;
                    height: 1px;
                    background-color: var(--tp-theme-primary);
                    @extend %tp-transition;
                }

                &:hover {
                    &::after {
                        left: 0;
                        right: auto;
                        width: 100%;
                    }
                }
            }
        }
    }

    .auth-card__body {
        .auth-input-icon {
            top: 10px;
        }

        label {
            &.form-label {
                font-size: 14px;
                color: var(--tp-common-black);
                line-height: 1;
            }

            &.form-check {
                .form-check-label {
                    font-size: 15px;
                    color: #55585b;
                    position: relative;
                    padding-left: 0.25rem;
                    z-index: 1;
                }
            }
        }

        input {
            &.form-control {
                border-radius: 0;
                height: 56px;
                background: #ffffff;
                border: 1px solid #e0e2e3;
                font-size: 14px;
                color: var(--tp-common-black);
                @include tp-placeholder {
                    color: #95999d;
                }

                &:focus {
                    border-color: var(--tp-common-black);
                    outline: none;
                    box-shadow: none;
                }
            }

            &.form-check-input {
                width: 18px;
                height: 18px;
                border-radius: 0;

                &:focus {
                    outline: none;
                    box-shadow: none;
                }
            }
        }

        .btn {
            font-weight: 500;
            font-size: 16px;
            color: var(--tp-common-white);
            background-color: var(--tp-common-black);
            padding: 14px 30px;
            text-align: center;
            display: inline-block;
            border-radius: 0;
            border: none;

            &:hover {
                background-color: var(--tp-theme-primary);
                color: var(--tp-common-white);
            }
        }

        a {
            text-decoration: none !important;
            font-weight: 400;
            font-size: 15px;
            color: var(--tp-theme-primary);
            position: relative;
            display: inline-block;

            &::after {
                position: absolute;
                content: '';
                left: auto;
                right: 0;
                bottom: 4px;
                width: 0%;
                height: 1px;
                background-color: var(--tp-theme-primary);
                @extend %tp-transition;
            }

            &:hover {
                &::after {
                    left: 0;
                    right: auto;
                    width: 100%;
                }
            }
        }
    }

    &-option {
        &-item {
            margin-bottom: 10px;
            &:not(:last-child) {
                margin-right: 10px;
            }
            &.has-google {
                & a {
                    width: 252px;

                    & img {
                        margin-right: 7px;
                    }
                }
            }
            & a {
                display: inline-block;
                width: 98px;
                height: 56px;
                line-height: 54px;
                text-align: center;
                border: 1px solid #e0e2e3;
                font-size: 16px;
                color: #041226;

                & img {
                    @include transform(translateY(-2px));

                    &.apple {
                        @include transform(translateY(-3px));
                    }
                }

                &:hover {
                    border-color: var(--tp-common-black);
                }
            }
        }
    }

    &-shape {
        &-1,
        &-2,
        &-3,
        &-4 {
            position: absolute;
            z-index: -1;
        }
        &-1 {
            top: 7%;
            left: 18%;

            @media #{$xl} {
                left: 8%;
            }
            @media #{$lg} {
                left: 4%;
            }
        }
        &-2 {
            bottom: 38%;
            left: 26%;

            @media #{$xl} {
                left: 15%;
            }
            @media #{$lg} {
                left: 8%;
            }
        }
        &-3 {
            top: 7%;
            right: 22%;

            @media #{$xl} {
                right: 15%;
            }
            @media #{$lg} {
                right: 10%;
            }
        }
        &-4 {
            bottom: 27%;
            right: 20%;
            @media #{$xxl, $x3l} {
                right: 10%;
            }
            @media #{$xl} {
                right: 3%;
            }
            @media #{$lg} {
                right: -6%;
            }
        }
    }
}
