(()=>{function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.toStringTag||"@@toStringTag";function i(e,o,a,c){var i=o&&o.prototype instanceof u?o:u,s=Object.create(i.prototype);return t(s,"_invoke",function(e,t,o){var a,c,i,u=0,s=o||[],l=!1,p={p:0,n:0,v:n,a:m,f:m.bind(n,4),d:function(e,t){return a=e,c=0,i=n,p.n=t,d}};function m(e,t){for(c=e,i=t,r=0;!l&&u&&!o&&r<s.length;r++){var o,a=s[r],m=p.p,f=a[2];e>3?(o=f===t)&&(i=a[(c=a[4])?5:(c=3,3)],a[4]=a[5]=n):a[0]<=m&&((o=e<2&&m<a[1])?(c=0,p.v=t,p.n=a[1]):m<f&&(o=e<3||a[0]>t||t>f)&&(a[4]=e,a[5]=t,p.n=f,c=0))}if(o||e>1)return d;throw l=!0,t}return function(o,s,f){if(u>1)throw TypeError("Generator is already running");for(l&&1===s&&m(s,f),c=s,i=f;(r=c<2?n:i)||!l;){a||(c?c<3?(c>1&&(p.n=-1),m(c,i)):p.n=i:p.v=i);try{if(u=2,a){if(c||(o="next"),r=a[o]){if(!(r=r.call(a,i)))throw TypeError("iterator result is not an object");if(!r.done)return r;i=r.value,c<2&&(c=0)}else 1===c&&(r=a.return)&&r.call(a),c<2&&(i=TypeError("The iterator does not provide a '"+o+"' method"),c=1);a=n}else if((r=(l=p.n<0)?i:e.call(t,p))!==d)break}catch(e){a=n,c=1,i=e}finally{u=1}}return{value:r,done:l}}}(e,a,c),!0),s}var d={};function u(){}function s(){}function l(){}r=Object.getPrototypeOf;var p=[][a]?r(r([][a]())):(t(r={},a,(function(){return this})),r),m=l.prototype=u.prototype=Object.create(p);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,t(e,c,"GeneratorFunction")),e.prototype=Object.create(m),e}return s.prototype=l,t(m,"constructor",l),t(l,"constructor",s),s.displayName="GeneratorFunction",t(l,c,"GeneratorFunction"),t(m),t(m,c,"Generator"),t(m,a,(function(){return this})),t(m,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:i,m:f}})()}function t(e,n,r,o){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}t=function(e,n,r,o){if(n)a?a(e,n,{value:r,enumerable:!o,configurable:!o,writable:!o}):e[n]=r;else{var c=function(n,r){t(e,n,(function(e){return this._invoke(n,r,e)}))};c("next",0),c("throw",1),c("return",2)}},t(e,n,r,o)}function n(e,t,n,r,o,a,c){try{var i=e[a](c),d=i.value}catch(e){return void n(e)}i.done?t(d):Promise.resolve(d).then(r,o)}$((function(){"use strict";var t=function(e){$(".cartmini__area").html(e.cart_mini),$('[data-bb-value="cart-count"]').text(e.count);var t=$(".tp-cart-area");t.length&&t.replaceWith(e.cart_content),void 0!==Theme.lazyLoadInstance&&Theme.lazyLoadInstance.update()},r=function(e){var n;n=e?$(e).closest("form"):$("form.cart-form"),$.ajax({type:"POST",url:n.prop("action"),data:n.serialize(),success:function(e){var n=e.error,r=e.message,o=e.data;n&&Theme.showError(r),t(o)},error:function(e){return Theme.handleError(e)}})};window.onBeforeChangeSwatches=function(e,t){var n=t.closest("form");e&&(n.find('button[type="submit"]').prop("disabled",!0),n.find('button[data-bb-toggle="add-to-cart"]').prop("disabled",!0))},$(document).on("click",'[data-bb-toggle="remove-coupon"]',(function(e){e.preventDefault();var t=$(e.currentTarget);$.ajax({url:t.prop("href"),type:"POST",success:function(e){var t=e.error,n=e.message;t?Theme.showError(n):(Theme.showSuccess(n),r())},error:function(e){return Theme.handleError(e)}})})).on("click",'[data-bb-toggle="decrease-qty"]',(function(e){var t=$(e.currentTarget).parent().find("input"),n=parseInt(t.val())-1;n=n<1?1:n,t.val(n),t.trigger("change")})).on("click",'[data-bb-toggle="increase-qty"]',(function(e){var t=$(e.currentTarget).parent().find("input"),n=t.prop("max");n&&parseInt(t.val())>=parseInt(n)||(t.val(parseInt(t.val())+1),t.trigger("change"))})).on("change",'[data-bb-toggle="update-cart"]',(function(e){r(e.currentTarget)})).on("click",'[data-bb-toggle="change-product-filter-layout"]',(function(e){e.preventDefault();var t=$(e.currentTarget);t.addClass("active"),t.closest("li").siblings().find("button").removeClass("active"),$(".bb-product-form-filter").find('[name="layout"]').val(t.data("value")).trigger("change")})).on("click",'[data-bb-toggle="copy-coupon"]',function(){var t,r=(t=e().m((function t(n){var r,o,a,c;return e().w((function(e){for(;;)switch(e.n){case 0:if(n.preventDefault(),r=$(n.currentTarget),o=r.data("value"),a=r.find("span").text(),!navigator.clipboard){e.n=2;break}return e.n=1,navigator.clipboard.writeText(o);case 1:e.n=3;break;case 2:(c=document.createElement("input")).value=o,document.body.appendChild(c),c.select(),document.execCommand("copy"),document.body.removeChild(c);case 3:r.find("span").text(r.data("copied-message")),setTimeout((function(){return r.find("span").text(a)}),2e3);case 4:return e.a(2)}}),t)})),function(){var e=this,r=arguments;return new Promise((function(o,a){var c=t.apply(e,r);function i(e){n(c,o,a,i,d,"next",e)}function d(e){n(c,o,a,i,d,"throw",e)}i(void 0)}))});return function(e){return r.apply(this,arguments)}}()).on("click",'[data-bb-toggle="scroll-to-review"]',(function(e){if($(".nav-tabs button#nav-review-tab").length){e.preventDefault();var t=$(".nav-tabs button#nav-review-tab"),n=$(".product-review-container");t.length&&n.length&&(t.tab("show"),$("html, body").animate({scrollTop:n.offset().top-100}))}})).on("show.bs.modal","#product-quick-view-modal",(function(e){var t=$(e.currentTarget),n=$(e.relatedTarget);$.ajax({url:n.data("url")||n.prop("href"),type:"GET",beforeSend:function(){n.addClass("btn-loading"),t.find(".modal-content").css("min-height","40rem").html('<div class="loading-spinner"></div>')},success:function(e){var n=e.error,r=e.data;n||(t.find(".modal-content").css("min-height","0").html(r),void 0!==Theme.lazyLoadInstance&&Theme.lazyLoadInstance.update(),setTimeout((function(){EcommerceApp.initProductGallery(!0)}),100),document.dispatchEvent(new CustomEvent("ecommerce.quick-view.initialized")))},complete:function(){return n.removeClass("btn-loading")}})})).on("submit","form#coupon-form",(function(e){e.preventDefault();var t=$(e.currentTarget),n=t.find('button[type="submit"]');$.ajax({url:t.prop("action"),type:"POST",data:t.serialize(),beforeSend:function(){return n.prop("disabled",!0).addClass("btn-loading")},success:function(e){var t=e.error,n=e.message;t?Theme.showError(n):(Theme.showSuccess(n),r())},error:function(e){return Theme.handleError(e)},complete:function(){return n.prop("disabled",!1).removeClass("btn-loading")}})})).on("keyup","form#coupon-form input",(function(e){var t=$(e.currentTarget);t.closest("form").find('button[type="submit"]').prop("disabled",!t.val())})).on("click",'.product-form button[type="submit"]',(function(e){e.preventDefault();var n=$(e.currentTarget),r=n.closest("form"),o=r.serializeArray();""!==r.find('input[name="id"]').val()&&(o.push({name:"checkout",value:"checkout"===n.prop("name")?1:0}),$.ajax({type:"POST",url:r.prop("action"),data:o,beforeSend:function(){n.prop("disabled",!0).addClass("btn-loading")},success:function(e){var n=e.error,o=e.message,a=e.data;if(n)return Theme.showError(o),void(void 0!==(null==a?void 0:a.next_url)&&setTimeout((function(){window.location.href=a.next_url}),500));r.find('input[name="qty"]').val(1),void 0!==(null==a?void 0:a.next_url)?window.location.href=a.next_url:(t(a),window.themeOptions&&"yes"===window.themeOptions.ecommerce_auto_open_mini_cart&&($(".cartmini__area").addClass("cartmini-opened"),$(".body-overlay").addClass("opened")))},error:function(e){return Theme.handleError(e)},complete:function(){return n.prop("disabled",!1).removeClass("btn-loading")}}))})).on("click",".js-sale-popup-quick-view-button",(function(e){e.preventDefault(),$("#product-quick-view-modal").modal("show",e.currentTarget)})).on("change",".tp-shop-top-select select",(function(e){var t=$(e.currentTarget);$(".bb-product-form-filter").find('input[name="'.concat(t.prop("name"),'"]')).val(t.val()).trigger("submit")})).on("click",".bb-product-items-wrapper .pagination a",(function(e){e.preventDefault();var t=$(e.currentTarget),n=new URL(t.prop("href")).searchParams.get("page");$(".bb-product-form-filter").find('[name="page"]').val(n).trigger("change")})).on("submit","form.subscribe-form",(function(e){e.preventDefault();var t=$(e.currentTarget),n=t.find("button[type=submit]");$.ajax({type:"POST",cache:!1,url:t.prop("action"),data:new FormData(t[0]),contentType:!1,processData:!1,beforeSend:function(){return n.prop("disabled",!0).addClass("btn-loading")},success:function(e){var n=e.error,r=e.message;n?Theme.showError(r):(t.find("input").val(""),Theme.showSuccess(r),document.dispatchEvent(new CustomEvent("newsletter.subscribed")))},error:function(e){"undefined"!=typeof refreshRecaptcha&&refreshRecaptcha(),Theme.handleError(e)},complete:function(){"undefined"!=typeof refreshRecaptcha&&refreshRecaptcha(),n.prop("disabled",!1).removeClass("btn-loading")}})})).on("click",'[data-bb-toggle="product-tab"]',(function(e){e.preventDefault();var t=$(e.currentTarget),n=t.closest(".tp-product-area").find("#productTabContent .tab-pane"),r=n.closest(".tp-product-area"),o=t.find("span.tp-product-tab-tooltip"),a="".concat(t.closest("#productTab").data("ajax-url"),"&type=").concat(t.data("bb-value"));fetch(a,{method:"GET",headers:{"Content-Type":"application/json",Accept:"application/json"}}).then((function(e){if(!e.ok)throw new Error("Network response was not ok");return e.json()})).then((function(e){var t=e.data;o.text(t.count),n.html(t.html),void 0!==Theme.lazyLoadInstance&&Theme.lazyLoadInstance.update()})).catch((function(e){Theme.handleError(e)})).finally((function(){$(".loading-spinner").remove()})),o.text("..."),r.append('<div class="loading-spinner"></div>')})).on("submit",".contact-form",(function(e){e.preventDefault();var t=$(e.currentTarget),n=t.find("button[type=submit]");$.ajax({type:"POST",cache:!1,url:t.prop("action"),data:new FormData(t[0]),contentType:!1,processData:!1,beforeSend:function(){return n.addClass("button-loading")},success:function(e){var n=e.error,r=e.message;n?Theme.showError(r):(t[0].reset(),Theme.showSuccess(r))},error:function(e){return Theme.handleError(e)},complete:function(){"undefined"!=typeof refreshRecaptcha&&refreshRecaptcha(),n.removeClass("button-loading")}})})).on("click",".sticky-actions-button button",(function(e){e.preventDefault();var t=$(e.currentTarget),n=$("form.product-form");"add-to-cart"===t.prop("name")&&n.find('button[type="submit"][name="add-to-cart"]').trigger("click"),"checkout"===t.prop("name")&&n.find('button[type="submit"][name="checkout"]').trigger("click")})).on("click",'[data-bb-toggle="open-mini-cart"]',(function(e){$('[data-bb-toggle="mini-cart-content-slot"]').html('<div class="loading-spinner"></div>'),$.ajax({url:$(e.currentTarget).data("url"),type:"GET",success:function(e){var t=e.data;$('[data-bb-toggle="mini-cart-content-slot"]').html(t.content),$('[data-bb-toggle="mini-cart-footer-slot"]').html(t.footer),void 0!==Theme.lazyLoadInstance&&Theme.lazyLoadInstance.update()},error:function(e){return Theme.handleError(e)}})})),document.addEventListener("ecommerce.quick-view.initialized",(function(){var e=$(document).find("[data-countdown]");$(e).length&&$.fn.countdown&&e.countdown()})),document.addEventListener("ecommerce.cart.added",(function(e){var n=e.detail,r=n.data,o=(n.element,n.message);t(r),window.themeOptions&&"yes"===window.themeOptions.ecommerce_auto_open_mini_cart?($(".cartmini__area").addClass("cartmini-opened"),$(".body-overlay").addClass("opened")):Theme.showSuccess(o)})),document.addEventListener("ecommerce.cart.removed",(function(e){var n=e.detail.data;0===n.count&&($(".cartmini__area").removeClass("cartmini-opened"),$(".body-overlay").removeClass("opened")),t(n)})),document.addEventListener("ecommerce.wishlist.removed",(function(e){var t=e.detail,n=t.data;t.element.closest("tr").remove(),0===n.count&&window.location.reload()})),document.addEventListener("ecommerce.compare.added",(function(e){var t=e.detail.element;t.find("span")&&t.find("span").text(t.hasClass("active")?t.data("remove-text"):t.data("add-text"))})),document.addEventListener("ecommerce.wishlist.added",(function(e){var t=e.detail,n=t.data,r=t.element;n.added?r.addClass("active"):r.removeClass("active"),r.find("span")&&r.find("span").text(n.added?r.data("remove-text"):r.data("add-text"))})),document.addEventListener("ecommerce.compare.removed",(function(e){var t=e.detail.element;t.find("span")&&t.find("span").text(t.hasClass("active")?t.data("remove-text"):t.data("add-text"))})),document.addEventListener("ecommerce.product-filter.before",(function(){$(".tp-shop-area > .container, .bb-shop-detail > .container > .bb-shop-tab-content").append('<div class="loading-spinner"></div>')})),document.addEventListener("ecommerce.product-filter.success",(function(e){var t=e.detail.data;$(".bb-product-items-wrapper").html(t.data),t.additional&&$(".bb-shop-sidebar").replaceWith(t.additional.filters_html),$(".tp-shop-top-result p").text(t.message),$("html, body").animate({scrollTop:$(".tp-shop-main-wrapper").offset().top-120})})),document.addEventListener("ecommerce.product-filter.completed",(function(){$(".tp-shop-area > .container, .bb-shop-detail > .container > .bb-shop-tab-content").find(".loading-spinner").remove()})),document.addEventListener("ecommerce.quick-shop.before-send",(function(e){var t=e.detail,n=t.element,r=t.modal;n.addClass("btn-loading"),r.find(".modal-body").css("min-height","16rem").html('<div class="loading-spinner"></div>')})),document.addEventListener("ecommerce.quick-shop.completed",(function(e){var t=e.detail,n=t.element,r=t.modal;n.removeClass("btn-loading"),r.find(".modal-body").css("min-height","0")})),"#product-review"===window.location.hash&&$(document).find('[data-bb-toggle="scroll-to-review"]').trigger("click"),document.addEventListener("shortcode.loaded",(function(){var e=$(document).find("[data-countdown]");$(e).length&&$.fn.countdown&&e.countdown()}))}))})();