@use '../utils' as *;

/*----------------------------------------*/
/*  2.7 Carousel
/*----------------------------------------*/

/* tp range slider css */
.#{$theme-prefix}-range-slider {
    & .inside-slider {
        padding-left: 7px;
        padding-right: 17px;
    }
    & .noUi-target {
        background-color: #191c3c;
        border-radius: 12px;
        border: 0;
        box-shadow: none;
    }

    & .noUi-connect {
        background-color: var(--tp-theme-primary);
    }

    & .noUi-horizontal {
        height: 6px;
    }
    & .noUi-handle {
        height: 24px;
        width: 24px;
        background-color: var(--tp-theme-primary);
        border-radius: 50%;
        border: 4px solid var(--tp-common-white);
        box-shadow: 0px 4px 10px rgba(5, 9, 43, 0.3);
        top: -9px;

        &:hover {
            cursor: pointer;
        }
        &::before,
        &::after {
            display: none;
        }
    }

    &-dark {
        & .noUi-handle {
            border: 4px solid #2d314b;
        }
    }
}

/* tp swiper slider dot */

.#{$theme-prefix}-swiper-dot {
    line-height: 1;
    .swiper-pagination-bullet {
        width: 10px;
        height: 10px;
        background-color: var(--tp-grey-3);
        opacity: 1;
        margin: 0 3px;
        position: relative;
        @extend %tp-transition;
        & button {
            font-size: 0;
        }

        &.swiper-pagination-bullet-active {
            background-color: var(--tp-theme-primary);
        }
    }

    &.#{$theme-prefix}-swiper-dot-style-darkRed {
        & .swiper-pagination-bullet {
            &.swiper-pagination-bullet-active {
                background-color: var(--tp-theme-primary);
            }
        }
    }
}

.#{$theme-prefix}-swiper-dot-border {
    line-height: 1;
    .swiper-pagination-bullet {
        width: 12px;
        height: 12px;
        background-color: transparent;
        opacity: 1;
        margin: 0 3px;
        position: relative;
        @extend %tp-transition;
        border: 1px solid rgba($color: $black, $alpha: 0.4);
        & button {
            font-size: 0;
        }

        &.swiper-pagination-bullet-active {
            background-color: var(--tp-common-black);
            border-color: var(--tp-common-black);
        }

        &:hover {
            border-color: var(--tp-common-black);
        }
    }

    &.#{$theme-prefix}-swiper-dot-style-darkRed {
        & .swiper-pagination-bullet {
            &.swiper-pagination-bullet-active {
                background-color: var(--tp-theme-primary);
            }
        }
    }
}

.#{$theme-prefix}-swiper-arrow {
    & button {
        width: 40px;
        height: 40px;
        font-size: 20px;
        background-color: transparent;
        color: var(--tp-text-2);
        border: 1px solid rgba($color: $black, $alpha: 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
            background-color: var(--tp-theme-primary);
            color: var(--tp-common-white);
        }

        & svg {
            @extend %tp-svg-y-2;
        }
    }
}

/* tp swiper scrollbar */

.#{$theme-prefix}-swiper-scrollbar {
    background-color: #edeff2;
    height: 2px;
    @extend %tp-transition;
    & .#{$theme-prefix}-swiper-scrollbar-drag {
        background-color: var(--tp-common-black);
        height: 100%;

        &:hover {
            cursor: pointer;
        }
    }

    &:hover,
    &:focus {
        @include transform(scaleY(3));
    }
}

/* tp slick arrow */

.#{$theme-prefix}-slick-dot {
    & .slick-dots {
        & li {
            display: inline-block;
            margin: 0 4px;
            &.slick-active {
                & button {
                    background-color: var(--tp-common-white);
                }
            }
            & button {
                font-size: 0;
                width: 10px;
                height: 10px;
                border-radius: 50%;
                background-color: rgba($color: $white, $alpha: 0.2);
            }
        }
    }
    &-blue {
        & .slick-dots {
            & li {
                &.slick-active {
                    & button {
                        background-color: var(--tp-theme-primary);
                    }
                }
                & button {
                    background-color: var(--tp-grey-3);
                }
            }
        }
    }
}
