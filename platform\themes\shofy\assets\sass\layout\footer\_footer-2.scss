@use '../../utils' as *;

/*----------------------------------------*/
/*  6.2 Footer Style 2
/*----------------------------------------*/

.#{$theme-prefix}-footer {
    $self: &;
    &-style-2 {
        #{$self} {
            &-desc {
                @extend %tp-ff-jost;
                font-weight: 400;
                line-height: 1.3;
            }
            &-social {
                & a {
                    border: 1px solid #d9dbde;
                    box-shadow: none;
                    border-radius: 0;
                    &:hover {
                        background-color: var(--secondary-color);
                        border-color: var(--secondary-color);
                    }
                }
            }
            &-widget {
                &-title {
                    @extend %tp-ff-jost;
                    font-weight: 500;
                    font-size: 23px;
                    margin-bottom: 15px;
                }
                & ul {
                    & li {
                        & a {
                            @extend %tp-ff-jost;
                            font-size: 15px;
                            font-weight: 400;
                            color: var(--tp-text-2);
                            &::after {
                                top: 9px;
                                width: 3px;
                                height: 3px;
                                background-color: var(--tp-text-2);
                            }
                            &:hover {
                                color: var(--secondary-color);
                                &::after {
                                    background-color: var(--secondary-color);
                                }
                            }
                        }
                    }
                }
            }
            &-talk {
                & span {
                    @extend %tp-ff-jost;
                }
                & h4 {
                    @extend %tp-ff-jost;
                    font-weight: 500;
                    font-size: 22px;

                    & a {
                        &:hover {
                            color: var(--secondary-color);
                        }
                    }
                }
            }
            &-contact {
                &-content {
                    & p {
                        @extend %tp-ff-jost;
                        font-size: 16px;
                        & a {
                            &:hover {
                                color: var(--secondary-color);
                            }
                        }
                    }
                }
            }
            &-bottom-wrapper {
                border-color: #e4e5e8;
            }
            &-copyright {
                & p {
                    @extend %tp-ff-jost;
                    margin-bottom: 0;
                    font-size: 16px;
                    & a {
                        color: var(--secondary-color);
                    }
                }
            }
        }
        &#{$self}-style-primary {
            #{$self} {
                &-social {
                    & a {
                        border: 1px solid #e6e7e8;
                        &:hover {
                            background-color: var(--tp-theme-primary);
                            border-color: var(--tp-theme-primary);
                        }
                    }
                }
                &-widget {
                    & ul {
                        & li {
                            & a {
                                font-weight: 500;
                                &:hover {
                                    color: var(--tp-theme-primary);
                                    &::after {
                                        background-color: var(--tp-theme-primary);
                                    }
                                }
                            }
                        }
                    }
                }
                &-talk {
                    margin-bottom: 25px;
                    & h4 {
                        & a {
                            &:hover {
                                color: var(--tp-theme-primary);
                            }
                        }
                    }
                }
                &-contact {
                    &-content {
                        & p {
                            & a {
                                &:hover {
                                    color: var(--tp-theme-primary);
                                }
                            }
                        }
                    }
                }
                &-desc {
                    margin-bottom: 21px;
                    line-height: 1.44;
                }
                &-bottom-wrapper {
                    border-color: rgba($color: $black, $alpha: 0.1);
                }
                &-copyright {
                    & p {
                        & a {
                            color: var(--tp-theme-primary);
                        }
                    }
                }
            }
        }
        &#{$self}-style-6 {
            #{$self} {
                &-widget {
                    & ul {
                        & li {
                            &:not(:last-child) {
                                margin-bottom: 9px;
                            }
                            & a {
                                font-weight: 500;
                            }
                        }
                    }
                }
                &-desc {
                    font-size: 18px;
                    margin-bottom: 21px;
                    line-height: 1.44;
                }
                &-talk {
                    margin-bottom: 25px;
                }
                &-social {
                    & a {
                        border: 0;
                        box-shadow: 0px 1px 1px rgba(1, 15, 28, 0.2);
                        background-color: var(--tp-common-white);
                        color: var(--tp-common-black);
                        &:hover {
                            background-color: var(--tp-theme-primary);
                            color: var(--tp-common-white);
                        }
                    }
                }
            }
        }
    }
}
