<?php if($announcements->isNotEmpty()): ?>
    <div
        class="<?php echo \Illuminate\Support\Arr::toCssClasses([
            'ae-anno-announcement-wrapper',
            'hide-on-mobile' => \ArchiElite\Announcement\AnnouncementHelper::isHideOnMobile(),
            'ae-anno-announcement--dismissible' => setting('announcement_dismissible', false),
        ]); ?>"
        style="
            --background-color: <?php echo e(setting('announcement_background_color', theme_option('primary_color', '#000'))); ?>;
            --text-color: <?php echo e(setting('announcement_text_color', '#fff')); ?>;
            --font-size: <?php echo e(\ArchiElite\Announcement\AnnouncementHelper::getFontSize()); ?>;
        "
        <?php if($autoPlay = setting('announcement_autoplay', false)): ?>
            data-announcement-autoplay="<?php echo e($autoPlay); ?>"
            data-announcement-autoplay-delay="<?php echo e(setting('announcement_autoplay_delay', 5000)); ?>"
        <?php endif; ?>
    >
        <div
            class="ae-anno-announcement__items"
            style="
            justify-content: <?php echo e(\ArchiElite\Announcement\AnnouncementHelper::getTextAlignment()); ?>;
            <?php if(setting('announcement_text_alignment') === \ArchiElite\Announcement\Enums\TextAlignment::CENTER): ?> text-align: center; <?php endif; ?>
           max-width: <?php echo e(\ArchiElite\Announcement\AnnouncementHelper::getMaxWidth()); ?>;
        "
        >
            <?php if($announcements->count() > 1): ?>
                <?php echo $__env->make('plugins/announcement::partials.controls', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>

            <?php echo $__env->renderEach('plugins/announcement::partials.item', $announcements, 'announcement'); ?>
        </div>

        <?php if(setting('announcement_dismissible', false)): ?>
            <?php echo $__env->make('plugins/announcement::partials.dismiss', [
                'announcementIds' => $announcements->pluck('id')->toJson(),
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform/plugins/announcement/resources/views/announcements.blade.php ENDPATH**/ ?>