<?php

namespace Bo<PERSON>ble\RequestLog\Providers;

use Bo<PERSON>ble\RequestLog\Events\RequestHandlerEvent;
use Bo<PERSON>ble\RequestLog\Listeners\RequestHandlerListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        RequestHandlerEvent::class => [
            RequestHandlerListener::class,
        ],
    ];
}
