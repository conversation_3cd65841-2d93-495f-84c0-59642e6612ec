@use '../../utils' as *;
/*----------------------------------------*/
/*  3.2 Header Style 2
/*----------------------------------------*/

.#{$theme-prefix}-header {
    $self: &;
    &-top {
        &-border {
            border-bottom: 1px solid rgba($color: $black, $alpha: 0.1);
        }
        &-black {
            #{$self} {
                &-top {
                    &-menu {
                        &-item {
                            position: relative;
                            padding: 7px 0;
                            &:not(:last-child) {
                                padding-inline-end: 15px;
                                margin-inline-end: 17px;
                                &::after {
                                    position: absolute;
                                    content: '';
                                    inset-inline-end: 0;
                                    top: 50%;
                                    width: 1px;
                                    height: 20px;
                                    background-color: rgba($color: $black, $alpha: 0.1);
                                    @include transform(translateY(-50%));
                                }
                            }

                            & > span,
                            a {
                                padding-inline-start: 0;
                                font-size: 14px;
                                @extend %tp-ff-jost;
                                color: var(--tp-common-black);
                            }
                        }
                    }
                }
            }
        }
    }

    &-style-darkRed {
        #{$self} {
            &-action {
                &-badge {
                    background-color: var(--secondary-color);
                }
                &-item {
                    &:last-child {
                        @media #{$md, $sm, $xs} {
                            margin-inline-end: 0;
                        }
                    }
                }
                &-btn {
                    &:hover {
                        color: var(--secondary-color);
                    }
                }
            }
            &-top-menu-item {
                & ul {
                    & li {
                        & a {
                            &:hover {
                                color: var(--secondary-color);
                            }
                        }
                    }
                }
            }
        }
        .tp-header-sticky.header-sticky .tp-header-action-item .tp-header-action-btn .tp-header-action-badge {
            background-color: var(--secondary-color);
            border-color: var(--tp-common-white);
            color: var(--tp-common-white);
        }
        .tp-header-sticky.header-sticky .tp-header-action-item .tp-header-action-btn:hover {
            color: var(--secondary-color);
        }
    }
    &-style-primary {
        box-shadow: 0px 2px 6px rgba(1, 15, 28, 0.16);
        position: relative;
        z-index: 11;
        #{$self} {
            &-action {
                &-badge {
                    background-color: var(--tp-theme-primary);
                }
                &-item {
                    & button {
                        &:hover {
                            color: var(--tp-theme-primary);
                        }
                    }
                    &:last-child {
                        @media #{$md, $sm, $xs} {
                            margin-inline-end: 0;
                        }
                    }
                }
            }
            &-top-menu-item {
                & ul {
                    & li {
                        & a {
                            &:hover {
                                color: var(--tp-theme-primary);
                            }
                        }
                    }
                }
            }
        }

        .main-menu.menu-style-2 > nav > ul > li .tp-submenu li:hover > a {
            color: var(--tp-theme-primary);
        }
        .main-menu.menu-style-2 > nav > ul > li .tp-submenu li:hover > a::after {
            color: var(--tp-theme-primary);
        }
        .tp-header-sticky.header-sticky .tp-header-action-item .tp-header-action-btn .tp-header-action-badge {
            background-color: var(--tp-theme-primary);
            border-color: var(--tp-theme-primary);
            color: var(--tp-common-white);
        }

        .tp-header-sticky.header-sticky .tp-header-action-item button .tp-header-action-badge {
            background-color: var(--tp-theme-primary);
            border-color: var(--tp-common-white);
            color: var(--tp-common-white);
        }

        & .tp-header-info-item a {
            & span {
                color: var(--tp-theme-primary);
            }

            &:hover {
                color: var(--tp-theme-primary);
            }
        }

        .tp-header-search-2 input:focus {
            border-color: var(--tp-theme-primary);
        }
        .tp-header-search-2 button:hover {
            color: var(--tp-theme-primary);
        }

        .main-menu.menu-style-2 ul li:hover > a,
        .main-menu.menu-style-2 ul li:hover > a::after {
            color: var(--tp-theme-primary);
        }

        .main-menu.menu-style-2 > nav > ul > li .home-menu-title a:hover {
            color: var(--tp-theme-primary);
        }

        .main-menu.menu-style-2 .tp-menu-showcase-btn-2 {
            border-radius: 0;
            background-color: var(--tp-theme-primary);
        }

        .tp-header-sticky.header-sticky .tp-header-action-item .tp-header-action-btn:hover {
            color: var(--tp-theme-primary);
        }

        .main-menu.menu-style-2 > nav > ul > li .home-menu-item:hover .home-menu-title {
            color: var(--tp-theme-primary);
        }
    }

    &-info {
        &-item {
            position: relative;
            &:not(:last-child) {
                padding-inline-end: 20px;
                margin-inline-end: 20px;
                &::after {
                    position: absolute;
                    content: '';
                    inset-inline-end: 0;
                    top: 50%;
                    width: 1px;
                    height: 20px;
                    background-color: rgba($color: $black, $alpha: 0.1);
                    @include transform(translateY(-50%));
                }
            }
            & a {
                font-size: 15px;
                color: rgba($color: $black, $alpha: 0.8);

                & span {
                    color: var(--tp-theme-primary);
                    margin-inline-end: 5px;
                    & svg {
                        width: 1.25rem;
                        height: 1.25rem;
                        @extend %tp-svg-y-2;
                    }
                }
                &:hover {
                    color: var(--tp-theme-primary);
                }
            }
        }
    }

    &-search-2 {
        width: 100%;
        position: relative;
        & input {
            background: var(--tp-grey-4);
            border: 1px solid rgba(1, 15, 28, 0.1);
            height: 46px;
            padding-inline-start: 24px;
            padding-inline-end: 50px;
            @include tp-placeholder {
                color: var(--tp-text-1);
            }
            &:focus {
                border-color: var(--tp-theme-primary);
            }
        }
        & button {
            position: absolute;
            top: 50%;
            inset-inline-end: 20px;
            @include transform(translateY(-50%));
            font-size: 24px;

            &:hover {
                color: var(--tp-theme-primary);
            }

            & svg {
                @extend %tp-svg-y-3;
            }
        }
    }
    &-bottom {
        &-2 {
            @media #{$lg, $md, $sm, $xs} {
                padding-top: 15px;
                padding-bottom: 15px;
            }
        }
    }
}
