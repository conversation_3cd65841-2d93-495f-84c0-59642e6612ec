@use '../../utils' as *;

/*----------------------------------------*/
/*  8.24 Deal CSS
/*----------------------------------------*/

.#{$theme-prefix}-deal {
    &-title {
        font-weight: 500;
        font-size: 60px;
        line-height: 1.17;
        margin-bottom: 35px;

        @media #{$xs} {
            font-size: 45px;
        }

        &-pre {
            font-weight: 400;
            font-size: 18px;
            color: var(--tp-theme-primary);
            position: relative;
            z-index: 1;
            display: inline-block;
            margin-bottom: 7px;
            & svg {
                position: absolute;
                bottom: -8px;
                inset-inline-start: 26px;
                z-index: -1;
            }
        }
    }
    &-countdown {
        & .tp-product-countdown-inner {
            & ul {
                & li {
                    padding-top: 13px;
                    width: 76px;
                    height: 80px;
                    font-weight: 400;
                    font-size: 15px;
                    color: var(--tp-text-2);
                    box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.1);
                    border-radius: 8px;
                    position: relative;
                    border: none;

                    @media #{$xs} {
                        margin-bottom: 15px;
                    }

                    & span {
                        font-weight: 500;
                        font-size: 34px;
                        letter-spacing: -0.04em;
                        color: var(--tp-theme-primary);
                    }

                    &:not(:last-child) {
                        margin-right: 13px;
                        &::after,
                        &::before {
                            position: absolute;
                            content: '';
                            right: -10px;
                            top: 28px;
                            width: 5px;
                            height: 5px;
                            border-radius: 50%;
                            background-color: rgba($color: $black, $alpha: 0.2);
                        }
                        &::before {
                            top: 42px;
                        }
                    }
                }
            }
        }
    }
    &-shape {
        &-1 {
            position: absolute;
            top: 8%;
            left: 0;
            z-index: -1;
        }
        &-2 {
            position: absolute;
            top: 10%;
            left: 2%;
            z-index: -1;

            @media #{$xl} {
                left: -12%;
            }
            @media #{$lg} {
                left: -16%;
            }
            @media #{$md} {
                top: -4%;
                left: -32%;
            }
            @media #{$sm} {
                left: -47%;
            }
            @media #{$xs} {
                left: -100%;
            }
        }
        &-3 {
            position: absolute;
            top: 29%;
            right: 6%;
            z-index: -1;
        }
        &-4 {
            position: absolute;
            top: 11%;
            right: 5%;
            z-index: -1;

            @media #{$xl} {
                right: -10%;
            }

            @media #{$lg} {
                right: -16%;
            }
            @media #{$md} {
                right: -38%;
            }
            @media #{$sm} {
                right: -49%;
            }
            @media #{$xs} {
                right: -111%;
            }
        }
        &-5 {
            position: absolute;
            top: 3%;
            right: 28%;
            z-index: -1;
        }
        &-6 {
            position: absolute;
            bottom: 77px;
            left: 35%;
            z-index: -1;
        }
        &-7 {
            position: absolute;
            top: 10%;
            left: 50%;
            @include transform(translateX(-50%));
            z-index: -1;
        }
    }
}
