@use '../../utils' as *;

/*----------------------------------------*/
/*  8.6 Tooltip CSS START
/*----------------------------------------*/

.#{$theme-prefix}-product {
    &-tooltip {
        font-family: var(--primary-font);
        position: absolute;
        top: 50%;
        @include transform(translateY(-50%));
        right: 100%;
        font-weight: 500;
        font-size: 12px;
        color: var(--tp-common-white);
        background-color: var(--tp-common-black);
        display: inline-block;
        width: max-content;
        line-height: 1;
        padding: 4px 6px;
        border-radius: 4px;
        visibility: hidden;
        opacity: 0;
        z-index: 1;
        @include tp-transition-mul(
            (
                opacity 0.3s ease,
                visibility 0.3s ease,
                transform 0.3s cubic-bezier(0.71, 1.7, 0.77, 1.24),
                -webkit-transform 0.3s cubic-bezier(0.71, 1.7, 0.77, 1.24)
            )
        );
        @extend %tp-transition;

        &::before {
            position: absolute;
            content: '';
            right: -4px;
            top: 50%;
            @include transform(translateY(-50%));
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-left: 8px solid var(--tp-common-black);
            border-bottom: 8px solid transparent;
        }

        &-right {
            left: 100%;
            right: auto;

            &::before {
                left: -4px;
                right: auto;
                border-left: 0;
                border-right: 8px solid var(--tp-common-black);
            }
        }
    }
}
