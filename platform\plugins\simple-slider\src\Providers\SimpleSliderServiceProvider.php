<?php

namespace Bo<PERSON><PERSON>\SimpleSlider\Providers;

use Bo<PERSON>ble\Base\Facades\DashboardMenu;
use Bo<PERSON>ble\Base\Facades\PanelSectionManager;
use Bo<PERSON>ble\Base\PanelSections\PanelSectionItem;
use Bo<PERSON>ble\Base\Supports\DashboardMenuItem;
use Bo<PERSON>ble\Base\Supports\ServiceProvider;
use Botble\Base\Traits\LoadAndPublishDataTrait;
use Botble\Language\Facades\Language;
use Bo<PERSON>ble\Setting\PanelSections\SettingOthersPanelSection;
use Bo<PERSON>ble\SimpleSlider\Models\SimpleSlider;
use Botble\SimpleSlider\Models\SimpleSliderItem;
use Bo<PERSON>ble\SimpleSlider\Repositories\Eloquent\SimpleSliderItemRepository;
use Botble\SimpleSlider\Repositories\Eloquent\SimpleSliderRepository;
use Botble\SimpleSlider\Repositories\Interfaces\SimpleSliderInterface;
use Bo<PERSON>ble\SimpleSlider\Repositories\Interfaces\SimpleSliderItemInterface;

class SimpleSliderServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->bind(SimpleSliderInterface::class, function () {
            return new SimpleSliderRepository(new SimpleSlider());
        });

        $this->app->bind(SimpleSliderItemInterface::class, function () {
            return new SimpleSliderItemRepository(new SimpleSliderItem());
        });
    }

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/simple-slider')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions'])
            ->loadAndPublishViews()
            ->loadAndPublishTranslations()
            ->loadRoutes(['web', 'api'])
            ->loadMigrations()
            ->publishAssets();

        DashboardMenu::default()->beforeRetrieving(function (): void {
            DashboardMenu::make()
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-simple-slider')
                        ->priority(390)
                        ->name('plugins/simple-slider::simple-slider.menu')
                        ->icon('ti ti-slideshow')
                        ->route('simple-slider.index')
                );
        });

        PanelSectionManager::default()->beforeRendering(function (): void {
            PanelSectionManager::registerItem(
                SettingOthersPanelSection::class,
                fn () => PanelSectionItem::make('simple_sliders')
                    ->setTitle(trans('plugins/simple-slider::simple-slider.settings.title'))
                    ->withIcon('ti ti-slideshow')
                    ->withPriority(430)
                    ->withDescription(trans('plugins/simple-slider::simple-slider.settings.description'))
                    ->withRoute('simple-slider.settings')
            );
        });

        if (defined('LANGUAGE_MODULE_SCREEN_NAME') && defined('LANGUAGE_ADVANCED_MODULE_SCREEN_NAME')) {
            Language::registerModule(SimpleSlider::class);
        }

        $this->app->booted(function (): void {
            $this->app->register(HookServiceProvider::class);
        });
    }
}
