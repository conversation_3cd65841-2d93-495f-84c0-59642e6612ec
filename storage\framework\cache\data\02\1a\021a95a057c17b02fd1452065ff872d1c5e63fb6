1751222023O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:8:{i:0;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:1;s:4:"name";s:12:"New Arrivals";s:9:"parent_id";i:0;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:0;s:5:"image";N;s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";s:10:"ti ti-home";s:10:"icon_image";N;s:14:"products_count";i:6;}s:11:" * original";a:13:{s:2:"id";i:1;s:4:"name";s:12:"New Arrivals";s:9:"parent_id";i:0;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:0;s:5:"image";N;s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";s:10:"ti ti-home";s:10:"icon_image";N;s:14:"products_count";i:6;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:52;s:3:"key";s:12:"new-arrivals";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:1;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:52;s:3:"key";s:12:"new-arrivals";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:1;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:3;s:4:"name";s:8:"Featured";s:9:"parent_id";i:2;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:0;s:5:"image";s:34:"main/product-categories/menu-1.jpg";s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:6;}s:11:" * original";a:13:{s:2:"id";i:3;s:4:"name";s:8:"Featured";s:9:"parent_id";i:2;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:0;s:5:"image";s:34:"main/product-categories/menu-1.jpg";s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:6;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:54;s:3:"key";s:8:"featured";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:3;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:54;s:3:"key";s:8:"featured";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:3;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:4;s:4:"name";s:12:"New Arrivals";s:9:"parent_id";i:3;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:0;s:5:"image";N;s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:5;}s:11:" * original";a:13:{s:2:"id";i:4;s:4:"name";s:12:"New Arrivals";s:9:"parent_id";i:3;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:0;s:5:"image";N;s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:5;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:55;s:3:"key";s:12:"new-arrivals";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:4;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:55;s:3:"key";s:12:"new-arrivals";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:4;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:3;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:8;s:4:"name";s:10:"Top Brands";s:9:"parent_id";i:7;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:0;s:5:"image";N;s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:2;}s:11:" * original";a:13:{s:2:"id";i:8;s:4:"name";s:10:"Top Brands";s:9:"parent_id";i:7;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:0;s:5:"image";N;s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:2;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:59;s:3:"key";s:10:"top-brands";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:8;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:59;s:3:"key";s:10:"top-brands";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:8;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:4;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:2;s:4:"name";s:11:"Electronics";s:9:"parent_id";i:0;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:1;s:5:"image";N;s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";s:15:"ti ti-device-tv";s:10:"icon_image";N;s:14:"products_count";i:4;}s:11:" * original";a:13:{s:2:"id";i:2;s:4:"name";s:11:"Electronics";s:9:"parent_id";i:0;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:1;s:5:"image";N;s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";s:15:"ti ti-device-tv";s:10:"icon_image";N;s:14:"products_count";i:4;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:53;s:3:"key";s:11:"electronics";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:2;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:53;s:3:"key";s:11:"electronics";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:2;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:5;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:5;s:4:"name";s:12:"Best Sellers";s:9:"parent_id";i:3;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:1;s:5:"image";N;s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:10;}s:11:" * original";a:13:{s:2:"id";i:5;s:4:"name";s:12:"Best Sellers";s:9:"parent_id";i:3;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:1;s:5:"image";N;s:11:"is_featured";i:0;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:10;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:56;s:3:"key";s:12:"best-sellers";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:5;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:56;s:3:"key";s:12:"best-sellers";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:5;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:6;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:7;s:4:"name";s:19:"Computers & Laptops";s:9:"parent_id";i:2;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:1;s:5:"image";s:34:"main/product-categories/menu-2.jpg";s:11:"is_featured";i:1;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:4;}s:11:" * original";a:13:{s:2:"id";i:7;s:4:"name";s:19:"Computers & Laptops";s:9:"parent_id";i:2;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:1;s:5:"image";s:34:"main/product-categories/menu-2.jpg";s:11:"is_featured";i:1;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:4;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:58;s:3:"key";s:17:"computers-laptops";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:7;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:58;s:3:"key";s:17:"computers-laptops";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:7;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:7;O:39:"Botble\Ecommerce\Models\ProductCategory":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:6;s:4:"name";s:12:"Mobile Phone";s:9:"parent_id";i:3;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:2;s:5:"image";s:29:"main/product-categories/2.png";s:11:"is_featured";i:1;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:3;}s:11:" * original";a:13:{s:2:"id";i:6;s:4:"name";s:12:"Mobile Phone";s:9:"parent_id";i:3;s:11:"description";N;s:6:"status";s:9:"published";s:5:"order";i:2;s:5:"image";s:29:"main/product-categories/2.png";s:11:"is_featured";i:1;s:10:"created_at";s:19:"2025-06-25 22:46:02";s:10:"updated_at";s:19:"2025-06-25 22:46:02";s:4:"icon";N;s:10:"icon_image";N;s:14:"products_count";i:3;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:57;s:3:"key";s:12:"mobile-phone";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:6;s:6:"prefix";s:18:"product-categories";}s:11:" * original";a:5:{s:2:"id";i:57;s:3:"key";s:12:"mobile-phone";s:14:"reference_type";s:39:"Botble\Ecommerce\Models\ProductCategory";s:12:"reference_id";i:6;s:6:"prefix";s:18:"product-categories";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:9:"parent_id";i:2;s:11:"description";i:3;s:5:"order";i:4;s:6:"status";i:5;s:5:"image";i:6;s:11:"is_featured";i:7;s:4:"icon";i:8;s:10:"icon_image";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}