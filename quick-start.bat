@echo off
echo ========================================
echo Shofy E-commerce Quick Start Script
echo ========================================
echo.

echo Checking prerequisites...
echo.

echo Checking PHP...
php -v
if %errorlevel% neq 0 (
    echo ERROR: PHP is not installed or not in PATH
    echo Please install PHP 8.2+ from https://windows.php.net/download/
    pause
    exit /b 1
)

echo.
echo Checking Composer...
composer --version
if %errorlevel% neq 0 (
    echo ERROR: Composer is not installed or not in PATH
    echo Please install Composer from https://getcomposer.org/download/
    pause
    exit /b 1
)

echo.
echo Installing PHP dependencies...
composer install --optimize-autoloader

echo.
echo Generating application key...
php artisan key:generate

echo.
echo Building frontend assets...
npm run dev

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Create a MySQL database named 'shofy'
echo 2. Import one of the database files (database.sql, etc.)
echo 3. Update .env file with your database credentials
echo 4. Run: php artisan serve
echo 5. Visit: http://localhost:8000
echo.
echo Admin panel: http://localhost:8000/admin
echo ========================================
pause
