/*!
* jQuery meanMenu v2.0.8
* @Copyright (C) 2012-2014 <PERSON> @ MeanThemes (https://github.com/meanthemes/meanMenu)
*
*/
(function($){"use strict";$.fn.meanmenu=function(options){var defaults={meanMenuTarget:jQuery(this),meanMenuContainer:'body',meanMenuClose:"X",meanMenuCloseSize:"18px",meanMenuOpen:"<span /><span /><span />",meanRevealPosition:"right",meanRevealPositionDistance:"0",meanRevealColour:"",meanScreenWidth:"480",meanNavPush:"",meanShowChildren:!0,meanExpandableChildren:!0,meanRemoveAttrs:!1,onePage:!1,meanDisplay:"block",removeElements:""};options=$.extend(defaults,options);var currentWidth=window.innerWidth||document.documentElement.clientWidth;return this.each(function(){var meanMenu=options.meanMenuTarget;var meanContainer=options.meanMenuContainer;var meanMenuClose=options.meanMenuClose;var meanMenuCloseSize=options.meanMenuCloseSize;var meanMenuOpen=options.meanMenuOpen;var meanRevealPosition=options.meanRevealPosition;var meanRevealPositionDistance=options.meanRevealPositionDistance;var meanRevealColour=options.meanRevealColour;var meanScreenWidth=options.meanScreenWidth;var meanNavPush=options.meanNavPush;var meanRevealClass=".meanmenu-reveal";var meanShowChildren=options.meanShowChildren;var meanExpandableChildren=options.meanExpandableChildren;var meanExpand=options.meanExpand;var meanRemoveAttrs=options.meanRemoveAttrs;var onePage=options.onePage;var meanDisplay=options.meanDisplay;var removeElements=options.removeElements;var isMobile=!1;if((navigator.userAgent.match(/iPhone/i))||(navigator.userAgent.match(/iPod/i))||(navigator.userAgent.match(/iPad/i))||(navigator.userAgent.match(/Android/i))||(navigator.userAgent.match(/Blackberry/i))||(navigator.userAgent.match(/Windows Phone/i))){isMobile=!0}
    if((navigator.userAgent.match(/MSIE 8/i))||(navigator.userAgent.match(/MSIE 7/i))){jQuery('html').css("overflow-y","scroll")}
    var meanRevealPos="";var meanCentered=function(){if(meanRevealPosition==="center"){var newWidth=window.innerWidth||document.documentElement.clientWidth;var meanCenter=((newWidth/2)-22)+"px";meanRevealPos="left:"+meanCenter+";right:auto;";if(!isMobile){jQuery('.meanmenu-reveal').css("left",meanCenter)}else{jQuery('.meanmenu-reveal').animate({left:meanCenter})}}};var menuOn=!1;var meanMenuExist=!1;if(meanRevealPosition==="right"){meanRevealPos="right:"+meanRevealPositionDistance+";left:auto;"}
    if(meanRevealPosition==="left"){meanRevealPos="left:"+meanRevealPositionDistance+";right:auto;"}
    meanCentered();var $navreveal="";var meanInner=function(){if(jQuery($navreveal).is(".meanmenu-reveal.meanclose")){$navreveal.html(meanMenuClose)}else{$navreveal.html(meanMenuOpen)}};var meanOriginal=function(){jQuery('.mean-bar,.mean-push').remove();jQuery(meanContainer).removeClass("mean-container");jQuery(meanMenu).css('display',meanDisplay);menuOn=!1;meanMenuExist=!1;jQuery(removeElements).removeClass('mean-remove')};var showMeanMenu=function(){var meanStyles="background:"+meanRevealColour+";color:"+meanRevealColour+";"+meanRevealPos;if(currentWidth<=meanScreenWidth){jQuery(removeElements).addClass('mean-remove');meanMenuExist=!0;jQuery(meanContainer).addClass("mean-container");jQuery('.mean-container').prepend('<div class="mean-bar"><a href="#nav" class="meanmenu-reveal" style="'+meanStyles+'">Show Navigation</a><nav class="mean-nav"></nav></div>');var meanMenuContents=jQuery(meanMenu).html();jQuery('.mean-nav').html(meanMenuContents);if(meanRemoveAttrs){jQuery('nav.mean-nav ul, nav.mean-nav ul *').each(function(){if(jQuery(this).is('.mean-remove')){jQuery(this).attr('class','mean-remove')}else{jQuery(this).removeAttr("class")}
        jQuery(this).removeAttr("id")})}
        jQuery(meanMenu).before('<div class="mean-push" />');jQuery('.mean-push').css("margin-top",meanNavPush);jQuery(meanMenu).hide();jQuery(".meanmenu-reveal").show();jQuery(meanRevealClass).html(meanMenuOpen);$navreveal=jQuery(meanRevealClass);jQuery('.mean-nav ul').hide();if(meanShowChildren){if(meanExpandableChildren){jQuery('.mean-nav ul ul').each(function(){if(jQuery(this).children().length){jQuery(this,'li:first').parent().append('<a class="mean-expand" href="#" style="font-size: '+meanMenuCloseSize+'">'+meanExpand+'</a>')}});jQuery('.mean-expand').on("click",function(e){e.preventDefault();if(jQuery(this).hasClass("mean-clicked")){jQuery(this).prev('ul').slideUp(300,function(){});jQuery(this).parent().removeClass('dropdown-opened')}else{jQuery(this).prev('ul').slideDown(300,function(){});jQuery(this).parent().addClass('dropdown-opened')}
            jQuery(this).toggleClass("mean-clicked")})}else{jQuery('.mean-nav ul ul').show()}}else{jQuery('.mean-nav ul ul').hide()}
        jQuery('.mean-nav ul li').last().addClass('mean-last');$navreveal.removeClass("meanclose");jQuery($navreveal).click(function(e){e.preventDefault();if(menuOn===!1){$navreveal.css("text-align","center");$navreveal.css("text-indent","0");$navreveal.css("font-size",meanMenuCloseSize);jQuery('.mean-nav ul:first').slideDown();menuOn=!0}else{jQuery('.mean-nav ul:first').slideUp();menuOn=!1}
            $navreveal.toggleClass("meanclose");meanInner();jQuery(removeElements).addClass('mean-remove')});if(onePage){jQuery('.mean-nav ul > li > a:first-child').on("click",function(){jQuery('.mean-nav ul:first').slideUp();menuOn=!1;jQuery($navreveal).toggleClass("meanclose").html(meanMenuOpen)})}}else{meanOriginal()}};if(!isMobile){jQuery(window).resize(function(){currentWidth=window.innerWidth||document.documentElement.clientWidth;if(currentWidth>meanScreenWidth){meanOriginal()}else{meanOriginal()}
        if(currentWidth<=meanScreenWidth){showMeanMenu();meanCentered()}else{meanOriginal()}})}
    jQuery(window).resize(function(){currentWidth=window.innerWidth||document.documentElement.clientWidth;if(!isMobile){meanOriginal();if(currentWidth<=meanScreenWidth){showMeanMenu();meanCentered()}}else{meanCentered();if(currentWidth<=meanScreenWidth){if(meanMenuExist===!1){showMeanMenu()}}else{meanOriginal()}}});showMeanMenu()})}})(jQuery)
