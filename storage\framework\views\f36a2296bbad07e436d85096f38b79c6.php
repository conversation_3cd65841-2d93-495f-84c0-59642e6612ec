<?php
    $itemsPerRow = $shortcode->items_per_row ?: 4;
    $itemsPerRow = $shortcode->with_sidebar ? $itemsPerRow - 1 : $itemsPerRow;
?>

<section class="tp-product-arrival-area pt-30 pb-30"
     <?php if($shortcode->background_color): ?>
         style="background-color: <?php echo e($shortcode->background_color); ?> !important;"
     <?php endif; ?>
>
    <div class="container">
        <?php if($shortcode->title): ?>
            <div class="mb-40">
                <?php echo Theme::partial('section-title', compact('shortcode')); ?>

            </div>
        <?php endif; ?>

        <?php if($shortcode->with_sidebar): ?>
            <div class="row">
                <div class="col-xl-4 col-lg-5">
                    <?php echo $__env->make(Theme::getThemeNamespace('partials.shortcodes.ecommerce-products.partials.sidebar'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
                <div class="col-xl-8 col-lg-7">
                    <?php endif; ?>

                    <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.product-items'), ['itemsPerRow' => $itemsPerRow], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <?php if($shortcode->with_sidebar): ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/partials/shortcodes/ecommerce-products/grid.blade.php ENDPATH**/ ?>