@use '../../utils' as *;

/*----------------------------------------*/
/* 8.11 Product Card Style 3 CSS
/*----------------------------------------*/

.#{$theme-prefix}-product {
    $self: &;
    &-item-3 {
        &:hover {
            #{$self} {
                &-thumb-3 {
                    & img {
                        @include transform(scale(1.1));
                    }
                }
                &-action-3 {
                    right: 20px;
                    visibility: visible;
                    opacity: 1;
                }
                &-add-cart-btn-large-wrapper {
                    bottom: 0;
                    visibility: visible;
                    opacity: 1;
                }
            }
        }

        #{$self} {
            &-add-cart-btn-large-wrapper {
                #{$self}-add-cart-btn-large {
                    --tp-btn-color: var(--tp-common-white);

                    @extend %tp-ff-jost;
                    font-weight: 400;
                    font-size: 16px;
                    color: var(--tp-btn-color);
                    padding: 5px 30px;
                    &:hover {
                        background-color: var(--tp-theme-primary);
                    }
                }
            }
        }

        &#{$self}-style-primary {
            & #{$self} {
                &-title-3 {
                    & a {
                        &:hover {
                            color: var(--tp-theme-primary);
                        }
                    }
                }
                &-badge {
                    & span {
                        border-radius: 0;
                    }
                }
                &-thumb-3 {
                    & img {
                        @media #{$lg, $md, $sm, $xs} {
                            width: 100%;
                        }
                    }
                }
                &-add-cart-btn-large-wrapper {
                    #{$self} {
                        &-add-cart-btn-large {
                            &:hover {
                                background-color: var(--tp-theme-primary);
                            }
                        }
                    }
                }
            }
        }
    }
    &-title-3 {
        font-weight: 400;
        font-size: 20px;
        margin-bottom: 8px;
        & a {
            &:hover {
                color: var(--tp-theme-primary);
            }
        }
    }
    &-tag-3 {
        & a {
            position: relative;
            display: inline-block;
            line-height: 1;
            font-weight: 400;
            font-size: 12px;
            text-transform: uppercase;
            color: var(--tp-text-2);
            @extend %tp-transition;
            &:hover {
                color: var(--tp-theme-primary);
            }
        }
    }
    &-thumb-3 {
        & img {
            @extend %tp-transition;
            width: 100%;
            @media #{$sm, $xs} {
                width: 100%;
            }
        }
    }
    &-price {
        &-3 {
            font-weight: 500;
            font-size: 16px;
            color: var(--tp-common-black);

            &.new-price {
                color: var(--tp-common-black);
            }
            &.old-price {
                font-weight: 400;
                font-size: 14px;
                text-decoration-line: line-through;
                color: var(--tp-text-1);
            }
        }
    }
    &-action {
        &-3 {
            position: absolute;
            top: 20px;
            right: 0;
            bottom: auto;
            z-index: 1;
            visibility: hidden;
            opacity: 0;
            @extend %tp-transition;

            @media #{$xs} {
                top: 30px;
            }
        }
        &-btn-3 {
            --tp-btn-color: var(--tp-common-black);
            position: relative;
            display: inline-block;
            height: 40px;
            width: 40px;
            line-height: 40px;
            background: var(--tp-common-white);
            border: 1px solid #dfe0e1;
            box-shadow: 0px 1px 1px rgba(1, 15, 28, 0.1);
            margin-bottom: 4px;
            color: var(--tp-btn-color);
            &:last-child {
                margin-bottom: 0;
            }

            & svg {
                @extend %tp-svg-y-2;
            }

            #{$self} {
                &-tooltip {
                    @extend %tp-ff-jost;
                    font-weight: 600;
                    font-size: 12px;
                }
            }

            &:hover {
                --tp-btn-color: var(--tp-common-white);
                border-color: var(--tp-common-black);
                background: var(--tp-common-black);
                box-shadow: 0px 1px 1px rgba(1, 15, 28, 0.1);

                #{$self} {
                    &-tooltip {
                        visibility: visible;
                        opacity: 1;
                        @include transform(translateX(-8px) translateY(-50%));
                    }
                }
            }

            &.active {
                --tp-btn-color: var(--tp-common-white);
                background-color: var(--tp-theme-primary);
            }
        }
    }
    &-badge-3 {
        position: absolute;
        top: 20px;
        left: 20px;
        z-index: 1;
        & span {
            display: inline-block;
            width: 40px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            background-color: var(--tp-common-black);
            border-radius: 50%;
            color: var(--tp-common-white);
            font-weight: 500;
            font-size: 13px;
            letter-spacing: -0.02em;
        }
    }
    &-more-3 {
        & .#{$theme-prefix}-btn {
            font-weight: 400;
            font-size: 14px;
            padding: 6px 19px;
            color: var(--tp-common-black);
            background-color: var(--tp-common-white);
            border-color: var(--tp-common-white);
            &:hover {
                background-color: var(--tp-common-black);
                border-color: var(--tp-common-black);
                color: var(--tp-common-white);
            }
        }
    }
    &-tab {
        &-inner-3 {
            & .nav-tabs {
                border-bottom: 1px solid #dfe0e1;
                padding-bottom: 2px;
            }
        }
    }
}

/* special item */

.#{$theme-prefix}-special {
    $selfsp: &;
    &-item {
        .tp-product-thumb-3 {
            & img {
                @media #{$sm, $xs} {
                    width: 100%;
                }
            }
        }
    }
    &-slider {
        @media #{$xs} {
            padding-left: 20px;
            padding-right: 20px;
        }
        &-dot {
            .swiper-pagination-bullet.swiper-pagination-bullet-active {
                background-color: var(--tp-theme-primary);
            }
        }
        &-thumb {
            position: relative;
            height: 100%;
            margin-right: -13px;
            @media #{$sm, $xs} {
                min-height: 400px;
                margin-right: 0;
            }
            &-active {
                height: 100%;
            }
        }
    }
    &-arrow {
        & button {
            position: absolute;
            top: 34%;
            @extend %translateY1_2;
            left: -70px;
            width: 40px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            font-size: 16px;
            color: var(--tp-common-black);
            background-color: var(--tp-common-white);
            z-index: 1;

            &.tp-special-slider-button-next {
                left: auto;
                right: -70px;
            }

            & svg {
                @extend %tp-svg-y-2;
            }

            &:hover {
                background-color: var(--tp-common-black);
                color: var(--tp-common-white);
            }
        }
    }
    &-wrapper {
        margin-left: 12px;
        @media #{$sm, $xs} {
            margin-left: 0;
            margin-top: 10px;
        }
    }
    &-thumb {
        & img {
            @extend %bg-thumb;
            object-fit: cover;
        }
    }
    &-hotspot {
        $hot: &;
        &-1 {
            position: absolute;
            top: 35%;
            left: 19%;
        }
        &-2 {
            position: absolute;
            bottom: 17%;
            right: 22%;
        }
        &-item {
            width: 44px;
            &:hover {
                #{$hot} {
                    &-content {
                        visibility: visible;
                        opacity: 1;
                        @include transform(translate(-37%, 55px));
                    }
                }
            }
        }
        &-content {
            background-color: var(--tp-common-white);
            position: relative;
            min-width: 165px;
            padding: 15px 20px;
            @include transform(translate(-37%, 60px));
            transform-origin: top center;
            visibility: hidden;
            opacity: 0;
            @extend %tp-transition;
            &::after {
                position: absolute;
                content: '';
                left: 0;
                top: 0;
                width: 14px;
                height: 14px;
                background-color: var(--tp-common-white);
                top: 0;
                left: 50%;
                @include transform(translate(-50%, -50%) rotate(45deg));
            }

            & p {
                line-height: 1.2;
                margin-top: 10px;
                margin-bottom: 0;
            }
        }
        &-title {
            font-weight: 500;
            font-size: 16px;
            line-height: 1;
            text-align: center;
            margin-bottom: 0;
        }
    }
}
