<?php

namespace Bo<PERSON>ble\RequestLog\Providers;

use Bo<PERSON>ble\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\RequestLog\Commands\RequestLogClearCommand;
use Bo<PERSON>ble\RequestLog\Models\RequestLog;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Database\Console\PruneCommand;

class CommandServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        if (! $this->app->runningInConsole()) {
            return;
        }

        $this->commands([
            RequestLogClearCommand::class,
        ]);

        $this->app->afterResolving(Schedule::class, function (Schedule $schedule): void {
            $schedule->command(PruneCommand::class, ['--model' => RequestLog::class])->dailyAt('00:30');
        });
    }
}
