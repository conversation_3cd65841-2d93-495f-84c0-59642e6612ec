@use '../../utils' as *;

/*----------------------------------------*/
/*  8.19 Cart CSS
/*----------------------------------------*/

.#{$theme-prefix}-cart {
    &-header {
        &-product {
            padding-left: 30px !important;
        }
    }
    &-list {
        @media #{$lg, $md, $sm, $xs} {
            overflow-x: scroll;
        }
        .table > :not(caption) > * > * {
            padding: 20px 0;
            box-shadow: none;
            vertical-align: middle;
        }

        & thead {
            background-color: #f1f3f4;
            & th {
                font-weight: 500;
                font-size: 14px;
                color: var(--tp-common-black);
                border: 0 !important;
                padding-top: 9px !important;
                padding-bottom: 9px !important;
            }
        }
        & tr {
            & td {
                border-color: #e0e2e3;

                & .#{$theme-prefix}-cart-action-btn {
                    --tp-btn-color: #818487;

                    font-weight: 400;
                    font-size: 14px;
                    color: var(--tp-btn-color);

                    & svg {
                        @extend %tp-svg-y-2;
                    }

                    &:hover {
                        --tp-btn-color: #ff1826;
                    }
                }

                &.#{$theme-prefix}-cart-add-to-cart {
                    & .#{$theme-prefix}-btn {
                        border-radius: 0;
                    }
                }
            }
        }

        & tbody {
            & tr {
                &:first-child {
                    & td {
                        padding-top: 30px !important;
                    }
                }
            }
        }
    }
    &-img {
        width: 78px;
        margin-right: 20px;
        & img {
            width: 78px;
            height: 100px;
            object-fit: cover;
        }
    }
    &-title {
        & a {
            margin-inline-start: 20px;
            font-weight: 400;
            font-size: 16px;
            color: var(--tp-common-black);

            &:hover {
                color: var(--tp-theme-primary);
            }
        }
    }
    &-price {
        & span {
            font-size: 16px;
            color: var(--tp-common-black);
        }
    }
    &-quantity {
        width: 180px;
    }
    &-coupon {
        @media #{$sm, $xs} {
            margin-bottom: 20px;
        }
        &-input {
            &-box {
                & label {
                    font-size: 14px;
                    color: var(--tp-common-black);
                    margin-bottom: 7px;
                }
            }
            & input {
                background: #ffffff;
                border: 1px solid #d6d9dc;
                height: 46px;
                line-height: 46px;
                margin-right: 4px;
                max-width: 282px;
                @include tp-placeholder {
                    color: #818487;
                }
            }

            & button {
                --tp-btn-color: var(--tp-common-white);

                font-weight: 500;
                font-size: 16px;
                color: var(--tp-btn-color);
                background-color: var(--tp-common-black);
                padding: 10px 30px;

                &:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }

                &:hover {
                    background-color: var(--tp-theme-primary);
                }
            }
        }
    }
    &-update {
        &-btn {
            font-weight: 500;
            font-size: 16px;
            color: var(--tp-common-black);
            background-color: var(--tp-common-white);
            padding: 9px 29px;
            border: 1px solid rgba($color: $black, $alpha: 0.1);

            &:hover {
                background-color: var(--tp-theme-primary);
                border-color: var(--tp-theme-primary);
                color: var(--tp-common-white);
            }
        }
    }
    &-checkout {
        &-wrapper {
            background: var(--tp-common-white);
            box-shadow: 0px 30px 70px rgba(1, 15, 28, 0.1);
            padding: 36px 24px 28px;
            margin-inline-start: -24px;

            @media #{$md, $sm, $xs} {
                margin-top: 50px;
                margin-inline-start: 0;
            }
        }
        &-top {
            padding-bottom: 13px;
            margin-bottom: 19px;
            border-bottom: 1px solid #e0e2e3;
            & span {
                font-size: 20px;
                color: var(--tp-common-black);
                font-weight: 500;
            }
        }
        &-shipping {
            padding-bottom: 16px;
            border-bottom: 1px solid #e0e2e3;
            margin-bottom: 15px;
            &-title {
                font-weight: 500;
                font-size: 15px;
                margin-bottom: 7px;
            }
            &-option {
                &:not(:last-child) {
                    margin-bottom: 4px;
                }
                & input {
                    display: none;

                    &:checked {
                        & + label {
                            &::after {
                                border-color: var(--tp-theme-primary);
                            }
                            &::before {
                                opacity: 1;
                                visibility: visible;
                            }
                        }
                    }
                }

                & label {
                    font-weight: 400;
                    font-size: 14px;
                    color: #161c2d;
                    position: relative;
                    padding-left: 25px;
                    &:hover {
                        cursor: pointer;
                    }
                    & span {
                        color: var(--tp-theme-primary);
                    }

                    &::after {
                        position: absolute;
                        content: '';
                        left: 0;
                        top: 5px;
                        width: 16px;
                        height: 16px;
                        border-radius: 50%;
                        border: 1px solid #bcbcbc;
                        @extend %tp-transition;
                    }
                    &::before {
                        position: absolute;
                        content: '';
                        left: 4px;
                        top: 9px;
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        background-color: var(--tp-theme-primary);
                        visibility: hidden;
                        opacity: 0;
                        @extend %tp-transition;
                    }
                }
            }
        }
        &-total {
            margin-bottom: 25px;
            & span {
                font-weight: 500;
                font-size: 18px;
                color: var(--tp-common-black);
            }
        }
        &-btn {
            font-size: 16px;
            font-weight: 500;
            color: var(--tp-common-white);
            background-color: var(--tp-common-black);
            display: inline-block;
            padding: 10px 30px;
            text-align: center;
            &:hover {
                background-color: var(--tp-theme-primary);
                color: var(--tp-common-white);
            }
        }
    }
}
