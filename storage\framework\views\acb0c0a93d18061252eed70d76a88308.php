<div class="d-flex gap-3 mb-3">
    <?php echo e(RvMedia::image($store->logo_url, $store->name, 'thumb', attributes: ['class' => 'rounded-pill', 'style' => 'width: 70px; height: 70px'])); ?>

    <div>
        <h6>
            <a href="<?php echo e($store->url); ?>"><?php echo e($store->name); ?></a>
        </h6>
        <?php if(EcommerceHelper::isReviewEnabled()): ?>
            <div class="d-flex align-items-center gap-2">
                <?php echo $__env->make(EcommerceHelper::viewPath('includes.rating-star'), ['avg' => $store->reviews()->avg('star')], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <span class="small text-muted">
                    <?php if(($reviewsCount = $store->reviews()->count()) === 1): ?>
                        (<?php echo e(__('1 Review')); ?>)
                    <?php else: ?>
                        (<?php echo e(__(':count Reviews', ['count' => number_format($reviewsCount)])); ?>)
                    <?php endif; ?>
                </span>
            </div>
        <?php endif; ?>

        <time class="small text-muted" datetime="<?php echo e($store->created_at->toDateString()); ?>">
            <?php echo e(__('Joined :date', ['date' => Theme::formatDate($store->created_at)])); ?>

        </time>
    </div>
</div>

<ul class="d-flex flex-column gap-2 list-unstyled mb-3">
    <?php if(! MarketplaceHelper::hideStoreAddress() && $store->full_address): ?>
        <li class="d-flex align-items-center gap-2">
            <?php if (isset($component)) { $__componentOriginal73995948b3bd877b76251b40caf28170 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal73995948b3bd877b76251b40caf28170 = $attributes; } ?>
<?php $component = Botble\Icon\View\Components\Icon::resolve(['name' => 'ti ti-map-pin'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Botble\Icon\View\Components\Icon::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal73995948b3bd877b76251b40caf28170)): ?>
<?php $attributes = $__attributesOriginal73995948b3bd877b76251b40caf28170; ?>
<?php unset($__attributesOriginal73995948b3bd877b76251b40caf28170); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal73995948b3bd877b76251b40caf28170)): ?>
<?php $component = $__componentOriginal73995948b3bd877b76251b40caf28170; ?>
<?php unset($__componentOriginal73995948b3bd877b76251b40caf28170); ?>
<?php endif; ?>
            <strong><?php echo e(__('Address:')); ?></strong>
            <span><?php echo e($store->full_address); ?></span>
        </li>
    <?php endif; ?>

    <?php if(! MarketplaceHelper::hideStorePhoneNumber() && $store->phone): ?>
        <li class="d-flex align-items-center gap-2">
            <?php if (isset($component)) { $__componentOriginal73995948b3bd877b76251b40caf28170 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal73995948b3bd877b76251b40caf28170 = $attributes; } ?>
<?php $component = Botble\Icon\View\Components\Icon::resolve(['name' => 'ti ti-headphones'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Botble\Icon\View\Components\Icon::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal73995948b3bd877b76251b40caf28170)): ?>
<?php $attributes = $__attributesOriginal73995948b3bd877b76251b40caf28170; ?>
<?php unset($__attributesOriginal73995948b3bd877b76251b40caf28170); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal73995948b3bd877b76251b40caf28170)): ?>
<?php $component = $__componentOriginal73995948b3bd877b76251b40caf28170; ?>
<?php unset($__componentOriginal73995948b3bd877b76251b40caf28170); ?>
<?php endif; ?>
            <strong><?php echo e(__('Phone:')); ?></strong>
            <a href="tel:<?php echo e($store->phone); ?>"><?php echo e($store->phone); ?></a>
        </li>
    <?php endif; ?>

    <?php if(! MarketplaceHelper::hideStoreEmail() && $store->email): ?>
        <li class="d-flex align-items-center gap-2">
            <?php if (isset($component)) { $__componentOriginal73995948b3bd877b76251b40caf28170 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal73995948b3bd877b76251b40caf28170 = $attributes; } ?>
<?php $component = Botble\Icon\View\Components\Icon::resolve(['name' => 'ti ti-mail'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Botble\Icon\View\Components\Icon::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal73995948b3bd877b76251b40caf28170)): ?>
<?php $attributes = $__attributesOriginal73995948b3bd877b76251b40caf28170; ?>
<?php unset($__attributesOriginal73995948b3bd877b76251b40caf28170); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal73995948b3bd877b76251b40caf28170)): ?>
<?php $component = $__componentOriginal73995948b3bd877b76251b40caf28170; ?>
<?php unset($__componentOriginal73995948b3bd877b76251b40caf28170); ?>
<?php endif; ?>
            <strong><?php echo e(__('Email:')); ?></strong>
            <a href="mailto:<?php echo e($store->email); ?>"><?php echo e($store->email); ?></a>
        </li>
    <?php endif; ?>
</ul>

<p>
    <?php echo BaseHelper::clean($store->description ?: Str::words($store->content, 50)); ?>

</p>
<?php /**PATH C:\Users\<USER>\Downloads\Shofy v1.3.7 Nulled\Shofy v1.3.7 Nulled\codecanyon-51119638-shofy-ecommerce-multivendor-marketplace-laravel-platform\platform\themes/shofy/views/marketplace/includes/vendor-info.blade.php ENDPATH**/ ?>