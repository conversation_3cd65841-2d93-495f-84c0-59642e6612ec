@use '../../utils' as *;

/*----------------------------------------*/
/*  8.10 Product Card Style 2 CSS
/*----------------------------------------*/

.#{$theme-prefix}-product {
    $self: &;
    &-item-2 {
        &:hover {
            #{$self} {
                &-thumb-2 {
                    & img {
                        @include transform(scale(1.1));
                    }
                }
                &-action-2 {
                    left: 24px;
                    visibility: visible;
                    opacity: 1;
                }
            }
        }
    }
    &-title-2 {
        font-weight: 400;
        font-size: 20px;
        margin-bottom: 2px;
        & a {
            &:hover {
                color: var(--primary-color);
            }
        }
    }
    &-tag-2 {
        & a {
            font-size: 16px;
            position: relative;
            display: inline-block;
            line-height: 1;
            &::after {
                position: absolute;
                content: '';
                left: auto;
                right: 0;
                bottom: 0;
                width: 0;
                height: 1px;
                background-color: var(--tp-theme-primary);
                @extend %tp-transition;
            }

            &:hover {
                color: var(--tp-theme-primary);
                &::after {
                    left: 0;
                    right: auto;
                    width: 100%;
                }
            }
        }
    }
    &-price {
        &-2 {
            font-weight: 500;
            font-size: 16px;
            color: var(--tp-common-black);
            &.new-price {
                color: var(--tp-common-black);
            }
            &.old-price {
                font-weight: 400;
                font-size: 14px;
                text-decoration-line: line-through;
                color: var(--tp-text-1);
                margin-inline-start: 8px;
            }
        }
    }
    &-rating-icon-2 {
        margin-bottom: 4px;
    }
    &-action {
        &-2 {
            position: absolute;
            left: 0;
            top: 57px;
            right: auto;
            bottom: auto;
            z-index: 1;
            visibility: hidden;
            opacity: 0;
            @extend %tp-transition;

            @media #{$xs} {
                top: 30px;
            }
        }

        &-blackStyle {
            & #{$self} {
                &-action-btn-2 {
                    &:hover,
                    &.active {
                        background-color: var(--tp-common-black);
                        color: var(--tp-common-white);
                    }
                }
            }
        }
        &-primaryStyle {
            & #{$self} {
                &-action-btn-3 {
                    &:hover,
                    &.active {
                        background-color: var(--tp-theme-primary);
                        color: var(--tp-common-white);
                    }
                }
            }
        }

        &-btn-2 {
            --tp-btn-color: var(--tp-common-black);

            position: relative;
            display: inline-block;
            width: 42px;
            height: 42px;
            line-height: 42px;
            text-align: center;
            font-size: 18px;
            color: var(--tp-btn-color);
            background-color: var(--tp-common-white);
            border-bottom: 0;
            box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.2);
            border-radius: 50%;
            margin-bottom: 6px;

            & svg {
                @extend %tp-svg-y-1;
            }

            #{$self} {
                &-tooltip {
                    @extend %tp-ff-jost;
                }
            }

            &:hover {
                --tp-btn-color: var(--tp-common-white);
                background-color: var(--tp-common-black);

                #{$self} {
                    &-tooltip {
                        visibility: visible;
                        opacity: 1;
                        @include transform(translateX(8px) translateY(-50%));
                    }
                }
            }
        }
    }
    &-thumb-2 {
        & img {
            @extend %tp-transition;
        }
    }
}
