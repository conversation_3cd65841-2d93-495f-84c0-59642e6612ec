@use '../../utils' as *;

/*----------------------------------------*/
/*  5.1 Postbox css
/*----------------------------------------*/

// postbox css start
.#{$theme-prefix}-postbox {
    &-text {
        & p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
        }
    }
    &-meta {
        margin-bottom: 10px;
        & span {
            & i,
            & svg {
                color: var(--tp-theme-primary);
                margin-inline-end: 3px;
            }

            &:not(:last-child) {
                margin-inline-end: 20px;
            }
        }
    }
    &-title {
        font-size: 38px;
        margin-bottom: 12px;
        font-weight: 500;
        @media #{$lg} {
            font-size: 35px;
        }
        @media #{$md} {
            font-size: 35px;
        }

        @media #{$sm} {
            font-size: 30px;
        }

        @media #{$xs} {
            font-size: 25px;
        }

        & a {
            &:hover {
                color: var(--tp-theme-primary);
            }
        }
    }
    &-thumb {
        margin-bottom: 30px;
    }
    &-video {
        & .tp-postbox-video-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            @include transform(translate(-50%, -50%));
            color: var(--tp-common-black);
            border-radius: 50%;
            display: inline-block;
            width: 80px;
            height: 80px;
            line-height: 80px;
            text-align: center;
            background-color: var(--tp-common-white);
            @include animation(tp-pulse-2 2s infinite);
            &:hover {
                color: var(--tp-common-white);
                background-color: var(--tp-theme-primary);
            }
        }
    }
    &-audio {
        height: 420px;
        & iframe {
            width: 100%;
            height: 100%;
        }
    }
    &-nav {
        & button {
            width: 50px;
            height: 50px;
            text-align: center;
            line-height: 48px;
            background-color: var(--tp-common-white);
            color: var(--tp-common-black);
            position: absolute;
            top: 50%;
            left: 15px;
            @include transform(translateY(-50%));
            z-index: 1;
            font-size: 18px;

            &:hover {
                background-color: var(--tp-theme-primary);
                border-color: var(--tp-theme-primary);
                color: var(--tp-common-white);
            }

            &.tp-postbox-slider-button-next {
                right: 15px;
                left: auto;

                @include rtl {
                    left: 15px;
                    right: auto;
                }
            }
        }
    }
    &-details {
        &-category {
            margin-bottom: 6px;
            & span {
                font-size: 20px;
                color: var(--tp-theme-primary);
                display: inline-block;
            }
        }
        &-title {
            font-size: 60px;
            font-weight: 500;
            line-height: 1.07;
            margin-bottom: 12px;

            @media #{$md} {
                font-size: 50px;
            }
            @media #{$sm, $xs} {
                font-size: 35px;
            }
        }
        &-meta {
            & span {
                font-size: 15px;
                position: relative;

                &:not(:last-child) {
                    margin-inline-end: 13px;
                    padding-right: 9px;

                    &::after {
                        position: absolute;
                        content: '';
                        right: 0;
                        top: 50%;
                        @extend %translateY1_2;
                        width: 1px;
                        height: 20px;
                        background-color: #e6e7e8;
                    }
                }
                & svg {
                    @extend %tp-svg-y-2;
                }
                & i,
                & svg {
                    margin-inline-end: 1px;
                }

                &[data-meta='author'] {
                    & svg {
                        @include transform(translateY(-3px));
                    }
                }
            }
        }
        &-thumb {
            & img {
                margin-bottom: 62px;
                max-width: 100%;
            }
        }
        &-content {
            & p {
                font-size: 18px;
                line-height: 1.56;
                margin-bottom: 23px;
                &.tp-dropcap {
                    &::first-letter {
                        margin-top: 5px;
                    }
                }
            }
        }
        &-main-wrapper {
            padding-right: 100px;

            @media #{$lg, $md, $sm, $xs} {
                padding-right: 0;
            }
        }
        &-heading {
            font-weight: 500;
            font-size: 34px;
            letter-spacing: -0.02em;
            margin-bottom: 12px;
            margin-top: 43px;

            @media #{$sm} {
                font-size: 25px;
            }

            @media #{$xs} {
                font-size: 24px;
            }
        }
        &-desc-thumb {
            padding-left: 78px;
            padding-right: 78px;
            margin-top: 52px;
            margin-bottom: 35px;

            @media #{$sm} {
                padding-left: 28px;
                padding-right: 28px;
            }

            @media #{$xs} {
                padding-left: 15px;
                padding-right: 15px;
            }
            & img {
                max-width: 100%;
            }
            &-caption {
                font-size: 14px;
                color: var(--tp-text-body);
                margin-top: 7px;
                display: inline-block;
            }
        }
        &-quote {
            margin-bottom: 10px;
            margin-top: 50px;

            & p {
                line-height: 1.31;
                font-size: 26px;
                margin-bottom: 15px;
            }
        }
        &-list {
            padding-top: 6px;
            margin-bottom: 35px;
            & ul {
                & li {
                    list-style: none;
                    position: relative;
                    padding-left: 15px;
                    font-size: 18px;
                    color: var(--tp-common-black);
                    &:not(:last-child) {
                        margin-bottom: 7px;
                    }
                    &::after {
                        position: absolute;
                        content: '';
                        top: 11px;
                        left: 0;
                        width: 4px;
                        height: 4px;
                        background-color: var(--tp-text-body);
                        border-radius: 50%;
                    }
                }
            }
        }
        &-tags {
            @media #{$sm, $xs} {
                margin-bottom: 15px;
            }
            & span {
                font-size: 16px;
                font-weight: 500;
                color: var(--tp-common-black);
                margin-inline-end: 5px;
            }
            &.tagcloud {
                padding-top: 0;
                border: 0;
            }
        }
        &-share {
            &-wrapper {
                padding-top: 21px;
                padding-bottom: 33px;
            }
            & span {
                font-size: 16px;
                font-weight: 500;
                color: var(--tp-common-black);
                margin-inline-end: 5px;
            }
            & a {
                display: inline-block;
                width: 38px;
                height: 38px;
                line-height: 36px;
                text-align: center;
                border: 1px solid #e6e7e8;
                margin-inline-end: 4px;

                &:hover {
                    background-color: var(--tp-theme-primary);
                    border-color: var(--tp-theme-primary);
                    color: var(--tp-common-white);
                }

                &:last-child {
                    margin-inline-end: 0;
                }
            }

            &-2 {
                position: sticky;
                top: 120px;
                width: 55px;
                box-shadow: 0px 30px 40px rgba(1, 15, 28, 0.1);

                @media #{$sm, $xs} {
                    width: auto;
                    @include flexbox();
                    margin-bottom: 45px;
                    flex-wrap: wrap;
                }
                & span {
                    font-weight: 500;
                    font-size: 13px;
                    line-height: 1.23;
                    display: inline-block;
                    text-align: center;
                    padding-top: 18px;
                    color: var(--tp-common-black);
                    padding-bottom: 16px;
                    border-bottom: 1px solid #e6e7e8;

                    @media #{$sm, $xs} {
                        border-bottom: 0;
                        border-right: 1px solid #e6e7e8;
                        padding-left: 15px;
                        padding-right: 15px;
                    }
                }
                & ul {
                    @include flexbox();
                    flex-wrap: wrap;
                    & li {
                        list-style: none;
                        &:not(:last-child) {
                            border-bottom: 1px solid #e6e7e8;
                            @media #{$sm, $xs} {
                                border-bottom: 0;
                                border-right: 1px solid #e6e7e8;
                            }
                        }
                        & a {
                            display: inline-block;
                            width: 55px;
                            height: 55px;
                            line-height: 58px;
                            text-align: center;

                            &:hover {
                                color: var(--tp-theme-primary);
                            }
                        }
                    }
                }
            }
        }
        &-navigation {
            position: relative;
            padding: 35px 0 30px;
            border-top: 1px solid #e0e2e3;
            &::after {
                position: absolute;
                content: '';
                left: 50%;
                top: 50%;
                @include transform(translate(-50%, -50%));
                background-color: #e0e2e3;
                width: 1px;
                height: 80px;
            }
            &-icon {
                & span {
                    color: var(--tp-common-black);
                    font-size: 18px;

                    & svg {
                        @extend %tp-svg-y-2;
                    }
                    &:hover {
                        color: var(--tp-theme-primary);
                    }
                }
            }
            &-content {
                & span {
                    font-size: 15px;
                    display: inline-block;
                }
            }
            &-title {
                font-size: 18px;
                font-weight: 500;
                line-height: 1.22;
                & a {
                    &:hover {
                        color: var(--tp-theme-primary);
                    }
                }
            }
        }
        &-author {
            padding: 34px 44px 38px 45px;
            margin-bottom: 90px;

            @media #{$xs} {
                padding: 25px;
            }

            &-thumb {
                & img {
                    width: 90px;
                    height: 90px;
                    object-fit: cover;
                    border-radius: 50%;
                    margin-inline-end: 20px;

                    @media #{$xs} {
                        margin-inline-end: 0;
                        margin-bottom: 20px;
                    }
                }
            }
            &-content {
                & span {
                    font-size: 15px;
                    display: inline-block;
                }
                & p {
                    font-size: 16px;
                    line-height: 1.6;
                    margin-bottom: 19px;
                }
            }
            &-title {
                font-size: 20px;
                font-weight: 500;
                margin-bottom: 10px;
                & a {
                    &:hover {
                        color: var(--tp-theme-primary);
                    }
                }
            }
            &-social {
                & a {
                    display: inline-block;
                    width: 34px;
                    height: 34px;
                    line-height: 34px;
                    text-align: center;
                    background-color: #fff;
                    box-shadow: 0px 1px 1px rgba(1, 15, 28, 0.2);

                    &:hover {
                        background-color: var(--tp-theme-primary);
                        border-color: var(--tp-theme-primary);
                        color: var(--tp-common-white);
                    }
                }
            }
        }
        &-comment {
            &-wrapper {
                margin-bottom: 90px;
            }
            &-inner {
                ul {
                    &.children {
                        margin-left: 77px;

                        @media #{$xs} {
                            margin-left: 35px;
                        }
                    }
                    & li {
                        list-style: none;
                    }
                }
            }
            &-box {
                margin-bottom: 34px;
            }
            &-title {
                font-size: 24px;
                font-weight: 500;
                margin-bottom: 27px;
            }
            &-thumb {
                & img {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    margin-inline-end: 17px;

                    @media #{$xs} {
                        margin-inline-end: 0;
                        margin-bottom: 20px;
                    }
                }
            }
            &-top {
                margin-bottom: 8px;
            }
            &-avater {
                &-title {
                    font-size: 18px;
                    font-weight: 500;
                    margin-bottom: 0;
                    & a {
                        &:hover {
                            color: var(--tp-theme-primary);
                        }
                    }
                }
                &-meta {
                    font-size: 14px;
                    color: var(--tp-text-body);
                    display: inline-block;
                }
            }
            &-content {
                width: 100%;
                padding-bottom: 15px;
                border-bottom: 1px solid #e0e2e3;
                & p {
                    font-size: 14px;
                    line-height: 1.57;

                    @media #{$lg, $md, $sm, $xs} {
                        & br {
                            display: none;
                        }
                    }
                }
            }
            &-reply {
                & a {
                    font-size: 15px;
                    color: var(--tp-text-body);
                    display: inline-block;
                    line-height: 1;
                    border: 1px solid #e6e7e8;
                    padding: 6px 13px;

                    &:hover {
                        background-color: var(--tp-theme-primary);
                        border-color: var(--tp-theme-primary);
                        color: var(--tp-common-white);
                    }
                }
            }
        }
        &-form {
            & > p {
                line-height: 1;
                font-size: 16px;
                margin-bottom: 43px;
            }
            &-title {
                font-size: 24px;
                font-weight: 500;
                margin-bottom: 8px;
            }
            &-inner {
                margin-bottom: 6px;
            }
        }
        &-input {
            &-wrapper {
                margin-bottom: 8px;
            }
            &-box {
                position: relative;
                &:not(:last-child) {
                    margin-bottom: 34px;
                }
            }
            & input {
                height: 56px;
                background: #ffffff;
                border: 1px solid #e0e2e3;
                font-size: 14px;
                color: var(--tp-common-black);
                @include tp-placeholder {
                    color: #95999d;
                }
            }
            & textarea {
                height: 165px;
                resize: none;
            }
            &-title {
                & label {
                    font-size: 14px;
                    color: var(--tp-common-black);
                    position: absolute;
                    top: -7px;
                    left: 20px;
                    padding: 0 5px;
                    background-color: var(--tp-common-white);
                    line-height: 1;
                }
            }
            &-eye {
                position: absolute;
                right: 26px;
                top: 50%;
                @include transform(translateY(-50%));

                & .open-eye {
                    display: none;
                }

                & span {
                    @extend %tp-transition;
                }

                &:hover {
                    cursor: pointer;

                    & span {
                        color: var(--tp-common-black);
                    }
                }
            }
            &-btn {
                font-size: 16px;
                color: var(--tp-common-white);
                font-weight: 500;
                background-color: var(--tp-theme-primary);
                padding: 9px 30px;
                &:hover {
                    background-color: var(--tp-common-black);
                    color: var(--tp-common-white);
                }
            }
        }
        &-remeber {
            & input {
                display: none;
                &:checked {
                    & ~ label {
                        &::after {
                            background-color: var(--tp-theme-primary);
                            border-color: var(--tp-theme-primary);
                        }
                        &::before {
                            visibility: visible;
                            opacity: 1;
                        }
                    }
                }
            }

            & label {
                font-size: 15px;
                color: #55585b;
                position: relative;
                padding-left: 26px;
                z-index: 1;
                &::after {
                    position: absolute;
                    content: '';
                    top: 4px;
                    left: 0;
                    width: 18px;
                    height: 18px;
                    line-height: 16px;
                    text-align: center;
                    border: 1px solid #c3c7c9;
                    z-index: -1;
                    @include tp-transition(all, 0.2s);
                }
                &::before {
                    position: absolute;
                    content: url('../images/icons/check.svg');
                    top: 4px;
                    left: 0;
                    width: 18px;
                    height: 18px;
                    line-height: 16px;
                    text-align: center;
                    visibility: hidden;
                    opacity: 0;
                    color: var(--tp-common-white);
                    @include tp-transition(all, 0.2s);
                }

                & a {
                    &:hover {
                        color: var(--tp-theme-primary);
                    }
                }

                &:hover {
                    cursor: pointer;
                }
            }
        }
    }
    &-style2 {
        padding-right: 0;
    }
    &-related {
        &-title {
            font-size: 34px;
            font-weight: 600;
            margin-bottom: 35px;
        }
    }
}

// blockquote css start

blockquote {
    background: var(--tp-theme-primary);
    padding: 45px 58px;
    margin-bottom: 35px;
    position: relative;
    z-index: 1;
    @media #{$xs} {
        padding-left: 15px;
        padding-right: 15px;
    }
    & p {
        line-height: 1.31;
        font-size: 26px;
        color: #fff;
        font-weight: 400;
    }
    & cite {
        font-size: 18px;
        display: block;
        margin-top: 10px;
        color: #fff;
        font-style: inherit;
        font-weight: 600;
        position: relative;

        &::before {
            content: '';
            font-size: 28px;
            color: #fff;
            padding-bottom: 0px;
            display: inline-block;
            background: #fff;
            height: 2px;
            width: 40px;
            font-weight: 400;
            text-align: center;
            top: -4px;
            margin-inline-end: 10px;
            position: relative;
        }
    }

    .#{$theme-prefix}-postbox {
        &-details-quote-shape {
            &-1 {
                position: absolute;
                z-index: -1;
                right: 0;
                bottom: 0;
            }
            &-2 {
                position: absolute;
                z-index: -1;
                left: 30px;
                top: 30px;
            }
        }
    }
}
