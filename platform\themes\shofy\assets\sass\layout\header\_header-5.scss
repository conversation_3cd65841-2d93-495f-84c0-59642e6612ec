@use '../../utils' as *;
/*----------------------------------------*/
/*  3.4 Header Style 5
/*----------------------------------------*/

.#{$theme-prefix}-header {
    $self: &;

    &-sticky {
        &.header-sticky {
            & .tp-header-side-menu {
                box-shadow: 0px 2px 4px rgba(1, 15, 28, 0.16);
            }
        }
    }

    &-5 {
        padding-top: 13px;
        padding-bottom: 13px;

        @media#{$xs} {
            padding-left: 0;
            padding-right: 0;
        }
    }

    &-search-5 {
        margin-left: 22px;
        margin-right: 22px;

        @media #{$x4l, $x3l, $xxl} {
            margin-left: 60px;
            margin-right: 60px;
        }

        @media #{$xl} {
            margin-left: 100px;
            margin-right: 100px;
        }
    }

    &-search-input {
        &-box-5 {
            position: relative;

            & button {
                position: absolute;
                top: 50%;
                right: -1px;
                @extend %translateY1_2;
                font-weight: 500;
                font-size: 16px;
                color: var(--tp-common-white);
                background-color: var(--tp-common-black);
                padding: 7px 36px 11px;
                border-radius: 0 30px 30px 0;
            }
        }

        &-5 {
            & input {
                height: 44px;
                border: none;
                border-radius: 30px;
                padding-left: 52px;
                padding-right: 16rem;
                @include tp-placeholder {
                    color: #95999d;
                }
            }

            & span {
                position: absolute;
                top: 50%;
                left: 27px;
                font-size: 18px;
                color: var(--tp-common-black);
                @extend %translateY1_2;

                & svg {
                    @extend %tp-svg-y-2;
                }
            }
        }
    }

    &-action {
        &-5 {
            & button,
            & a {
                display: inline-block;
                position: relative;
                width: 38px;
                height: 38px;
                text-align: center;
                line-height: 38px;
                border-radius: 50%;
                background-color: rgba($color: $white, $alpha: 0.14);
                font-size: 16px;
                color: var(--tp-common-white);

                & svg {
                    @extend %tp-svg-y-2;
                }
            }
        }

        &-item-5 {
            margin-right: 8px;

            &:not(:last-child) {
                margin-right: 22px;
            }
        }

        &-badge-5 {
            position: absolute;
            top: -4px;
            right: -11px;
            display: inline-block;
            font-weight: 500;
            font-size: 12px;
            line-height: 1;
            letter-spacing: -0.1em;
            color: var(--tp-common-black);
            width: 26px;
            height: 26px;
            line-height: 20px;
            border-radius: 50%;
            border: 3px solid var(--tp-theme-primary);
            background-color: var(--tp-common-white);
        }
    }

    &-login {
        &-5 {
        }

        &-icon-5 {
            & span {
                font-size: 16px;
                color: var(--tp-common-white);
                background-color: rgba($color: $white, $alpha: 0.14);
                display: inline-block;
                width: 38px;
                height: 38px;
                line-height: 38px;
                text-align: center;
                border-radius: 50%;
                margin-right: 9px;

                & svg {
                    @include transform(translate(1px, -3px));
                }
            }
        }

        &-content-5 {
            & p {
                display: inline-block;
                font-weight: 500;
                font-size: 14px;
                line-height: 1.14;
                color: rgba($color: $white, $alpha: 1);
                margin-bottom: 0;

                & span {
                    color: rgba($color: $white, $alpha: 0.6);
                }
            }
        }
    }

    &-side-menu {
        position: absolute;
        top: 100%;
        left: 0;
        width: 280px;
        background-color: var(--tp-common-white);
        z-index: 99;
        padding: 39px 0 38px;
        box-shadow: 0px 1px 0px rgba(1, 15, 28, 0.06);
        @media #{$xl, $lg, $md, $sm, $xs} {
            display: none;
        }

        & ul {
            & li {
                list-style: none;
                position: relative;
                width: 100%;
                padding-left: 40px;
                padding-right: 40px;

                & a {
                    display: block;
                    font-size: 16px;
                    color: var(--tp-common-black);
                    position: relative;

                    & i {
                        display: inline-block;
                        width: 11%;
                        margin-right: 13px;
                        @include transform(translateY(4px));
                        font-size: 21px;
                        line-height: 1;
                    }

                    & .menu-text {
                        font-size: 16px;
                        line-height: 11px;
                        border-bottom: 1px solid #eaebed;
                        width: 80%;
                        display: inline-block;
                        padding: 19px 0 17px;
                    }
                }

                &:hover {
                    & > a {
                        color: var(--tp-theme-primary);

                        &::after {
                            color: var(--tp-theme-primary);
                        }
                    }

                    & .mega-menu {
                        visibility: visible;
                        opacity: 1;
                        top: 0;
                    }

                    & > .tp-submenu {
                        opacity: 1;
                        visibility: visible;
                        inset-inline-start: 100%;
                    }
                }

                &.has-dropdown {
                    & > a {
                        &::after {
                            position: absolute;
                            content: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M6 9l6 6l6 -6" /></svg>');
                            right: 0;
                            top: calc(50% + 3px);
                            @extend %translateY1_2;
                            color: #7f8387;
                            @extend %tp-transition;
                        }
                    }

                    &:hover {
                        & > a {
                            &::after {
                                color: var(--tp-theme-primary);
                            }
                        }
                    }
                }

                & .tp-submenu {
                    position: absolute;
                    left: 110%;
                    top: 0%;
                    visibility: hidden;
                    opacity: 0;
                    min-width: 300px;
                    background-color: var(--tp-common-white);
                    @include tp-transition(all, 0.3s);
                    padding-top: 10px;
                    padding-bottom: 10px;
                    display: block;

                    & li {
                        & a {
                            font-size: 15px;
                            padding: 6px 0 7px;
                        }

                        & ul {
                            display: block;
                        }
                    }
                }

                &:last-child {
                    & a {
                        & span {
                            border-bottom: 0;
                        }
                    }
                }

                & .mega-menu {
                    position: absolute;
                    top: 40px;
                    left: 100%;
                    background-color: var(--tp-common-white);
                    z-index: 1;
                    min-width: 670px;
                    @include flexbox();
                    box-shadow: 14px 20px 40px rgba(1, 15, 28, 0.14);
                    border-radius: 0px 8px 8px 0px;
                    visibility: hidden;
                    opacity: 0;
                    @extend %tp-transition;
                    /* left right */

                    & .home-menu {
                        padding: 20px 20px 0;

                        .home-menu-title a:hover {
                            color: var(--tp-theme-primary);
                        }
                    }

                    & *.menu-text {
                        display: none;
                    }

                    & .mega-menu-left {
                        width: 60%;
                    }

                    & .mega-menu-right {
                        width: 40%;
                        border: 4px solid var(--tp-common-white);
                        border-radius: 0 6px 6px 0;
                        padding-left: 34px;
                        padding-right: 33px;
                        padding-top: 30px;

                        &-title {
                            font-weight: 500;
                            font-size: 20px;
                            margin-bottom: 7px;
                        }
                    }

                    & .mega-menu-img {
                        height: 120px;
                        @include flexbox();
                        justify-content: center;
                        align-items: end;
                        margin-bottom: 40px;
                    }

                    & .mega-menu-list {
                        @include flexbox();
                        border-bottom: 1px solid #eaebed;

                        & ul {
                            padding-left: 34px;
                            padding-bottom: 30px;

                            &:not(:last-child) {
                                border-right: 1px solid #eaebed;
                            }

                            & li {
                                padding: 0;

                                &:not(:last-child) {
                                    margin-bottom: 4px;
                                }

                                & a {
                                    font-weight: 400;
                                    font-size: 15px;
                                    color: #55585b;

                                    &.mega-menu-title {
                                        font-weight: 500;
                                        font-size: 20px;
                                        color: var(--tp-common-black);
                                        margin-bottom: 10px;
                                    }

                                    &:hover {
                                        color: var(--tp-theme-primary);
                                    }
                                }

                                & ul {
                                    padding: 0;

                                    &:not(:last-child) {
                                        border: 0;
                                    }
                                }
                            }
                        }
                    }

                    & .mega-menu-brand {
                        display: flex;
                        justify-content: center;

                        & a {
                            margin: 0 27px;
                            display: inline-block;
                            border: 0;
                            padding: 26px 0 20px;

                            & img {
                                opacity: 0.5;
                                @extend %tp-transition;
                            }

                            & .menu-text {
                                display: none;
                            }

                            &:hover {
                                & img {
                                    opacity: 1;
                                }
                            }
                        }
                    }

                    & .menu-shop {
                        &-thumb {
                            & a {
                                border: 0;
                            }

                            & img {
                                width: 70px;
                                height: auto;
                                object-fit: cover;
                                margin-right: 17px;
                            }
                        }

                        &-item {
                            padding-top: 14px;

                            &:not(:last-child) {
                                padding-bottom: 15px;
                                border-bottom: 1px solid #eaebed;
                            }
                        }

                        &-meta {
                            & span {
                                font-size: 14px;
                                line-height: 1;
                                display: inline-block;
                                margin-bottom: 1px;

                                & a {
                                    color: #55585b;
                                    font-size: 14px;

                                    &:hover {
                                        color: var(--tp-theme-primary);
                                    }
                                }
                            }
                        }

                        &-title {
                            font-weight: 400;
                            font-size: 16px;
                            line-height: 0.7;

                            & a {
                                &:hover {
                                    color: var(--tp-theme-primary);
                                }
                            }
                        }

                        &-price {
                            font-weight: 500;
                            font-size: 15px;
                            line-height: 1;
                            letter-spacing: -0.02em;
                            color: var(--tp-common-black);

                            &.new-price {
                                color: var(--tp-common-black);
                            }

                            &.old-price {
                                font-weight: 400;
                                font-size: 13px;
                                text-decoration-line: line-through;
                                color: var(--tp-text-1);
                            }

                            &-wrapper {
                            }
                        }
                    }
                }
            }
        }

        &.sticky-active {
            display: none;
        }

        .home-menu-item:hover .home-menu-title {
            color: var(--tp-theme-primary);
        }
    }
}
